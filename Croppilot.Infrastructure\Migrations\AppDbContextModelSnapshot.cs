﻿// <auto-generated />
using System;
using Croppilot.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Croppilot.Infrastructure.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Croppilot.Date.Identity.ApplicationRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Croppilot.Date.Identity.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("OTPCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("OTPExpiration")
                        .HasColumnType("datetime2");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("Provider")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Croppilot.Date.Identity.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpiresOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("JwtTokenId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("RevokedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("Croppilot.Date.Models.AiModel.FeedbackEntry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Disease")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ModelResultId")
                        .HasColumnType("int");

                    b.Property<string>("Solution")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ModelResultId");

                    b.ToTable("FeedbackEntries");
                });

            modelBuilder.Entity("Croppilot.Date.Models.AiModel.ModelResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<Guid>("ImageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("AIModelResults");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Cart", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Carts");
                });

            modelBuilder.Entity("Croppilot.Date.Models.CartItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CartId")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("CartId");

                    b.HasIndex("ProductId");

                    b.ToTable("CartItems");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("Categories");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Daily harvested organic vegetables",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/0bdbd4c8-2503-427c-b4fe-ffa6e5c57a23_Fresh Vegetables.jpg",
                            Name = "Fresh Vegetables"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Naturally grown fruits with authentic flavors",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/5caaf20a-3510-4d38-b2b4-b16e79cf45ab_Seasonal Fruits.jpg",
                            Name = "Seasonal Fruits"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Fresh cheese and milk from our farm",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/61cd1282-c3c4-4ef3-9b2c-ae7d5beb482c_Dairy Products.jpg",
                            Name = "Dairy Products"
                        },
                        new
                        {
                            Id = 4,
                            Description = "Free-range chicken eggs",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/b7f5884a-6833-43f0-bb78-367c371a37fb_Organic Eggs.jpg",
                            Name = "Organic Eggs"
                        },
                        new
                        {
                            Id = 5,
                            Description = "Home garden flowers and decorative plants",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/9aa3dfb7-12b7-4631-908c-eb11fa0c64f9_Ornamental Plants.jpg",
                            Name = "Ornamental Plants"
                        },
                        new
                        {
                            Id = 6,
                            Description = "Vegetable and fruit starters for home gardening",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/28773f2f-5619-406e-8c74-1cd82b296d3f_Seedlings.jpg",
                            Name = "Seedlings"
                        },
                        new
                        {
                            Id = 7,
                            Description = "Natural feed for livestock and poultry",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/a5a834c1-736c-4858-95df-673e21014525_Organic Animal Feed.jpg",
                            Name = "Organic Animal Feed"
                        },
                        new
                        {
                            Id = 8,
                            Description = "Essential agricultural equipment",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/c10224a6-0625-41a3-8332-73433f96f39e_Farming Tools.jpg",
                            Name = "Farming Tools"
                        },
                        new
                        {
                            Id = 9,
                            Description = "Certified high-yield seeds",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/6c4bef01-1181-4fde-8bc4-78331a8c82d2_Premium Seeds.jpg",
                            Name = "Premium Seeds"
                        },
                        new
                        {
                            Id = 10,
                            Description = "Natural soil enhancers",
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/category-images/242d456f-5b41-4d3a-8744-cd18ec583aae_Organic Fertilizers.jpg",
                            Name = "Organic Fertilizers"
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.ChatHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("BotResponse")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("UserMessage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("ChatHistories");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Comment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int?>("ParentCommentId")
                        .HasColumnType("int");

                    b.Property<int>("PostId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("VoteCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.HasIndex("ParentCommentId");

                    b.HasIndex("PostId");

                    b.HasIndex("UserId");

                    b.ToTable("Comments");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Cupon", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Discount_Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Discount_Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("ExpirationDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("UsageCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<int>("UsageLimit")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("Cupons", t =>
                        {
                            t.HasCheckConstraint("CK_Cupon_Discount_Value", "Discount_Value > 0");

                            t.HasCheckConstraint("CK_Cupon_UsageLimit", "UsageLimit > 0");

                            t.HasCheckConstraint("ck_Cupon_ExpirationDate", "ExpirationDate > GetDate()");
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.DashboardModels.Alert", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("EmergencyType")
                        .HasColumnType("int");

                    b.Property<double>("Latitude")
                        .HasColumnType("float");

                    b.Property<string>("LocationDescription")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Longitude")
                        .HasColumnType("float");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Severity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("EmergencyAlerts");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 6, 26, 11, 57, 13, 537, DateTimeKind.Utc).AddTicks(9810),
                            EmergencyType = 5,
                            Latitude = 26.820553,
                            LocationDescription = "Farm Field #1",
                            Longitude = 30.802498,
                            Message = "Low moisture detected in Field A",
                            Severity = 2
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2025, 6, 26, 11, 42, 13, 537, DateTimeKind.Utc).AddTicks(9816),
                            EmergencyType = 4,
                            Latitude = 27.820553,
                            LocationDescription = "Farm Field #2",
                            Longitude = 31.802498,
                            Message = "Pest activity reported in Wheat field",
                            Severity = 1
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2025, 6, 26, 11, 27, 13, 537, DateTimeKind.Utc).AddTicks(9817),
                            EmergencyType = 0,
                            Latitude = 28.820553,
                            LocationDescription = "Farm Field #3",
                            Longitude = 32.802498,
                            Message = "Tractor requires maintenance",
                            Severity = 0
                        },
                        new
                        {
                            Id = 4,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 12, 13, 537, DateTimeKind.Utc).AddTicks(9818),
                            EmergencyType = 3,
                            Latitude = 29.820553,
                            LocationDescription = "Farm Field #4",
                            Longitude = 33.802498,
                            Message = "Storm warning for tonight",
                            Severity = 2
                        },
                        new
                        {
                            Id = 5,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 7, 13, 537, DateTimeKind.Utc).AddTicks(9820),
                            EmergencyType = 6,
                            Latitude = 30.820553,
                            LocationDescription = "Farm Field #5",
                            Longitude = 34.802498,
                            Message = "High pH level detected in Field B",
                            Severity = 1
                        },
                        new
                        {
                            Id = 6,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 22, 13, 537, DateTimeKind.Utc).AddTicks(9821),
                            EmergencyType = 5,
                            Latitude = 31.820553,
                            LocationDescription = "Farm Field #6",
                            Longitude = 35.802498,
                            Message = "Low moisture detected in Field C",
                            Severity = 2
                        },
                        new
                        {
                            Id = 7,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 17, 13, 537, DateTimeKind.Utc).AddTicks(9822),
                            EmergencyType = 4,
                            Latitude = 32.820552999999997,
                            LocationDescription = "Farm Field #7",
                            Longitude = 36.802498,
                            Message = "Pest activity reported in Corn field",
                            Severity = 1
                        },
                        new
                        {
                            Id = 8,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 24, 13, 537, DateTimeKind.Utc).AddTicks(9824),
                            EmergencyType = 1,
                            Latitude = 26.820553,
                            LocationDescription = "Farm Field #1",
                            Longitude = 30.802498,
                            Message = "Medical emergency: Worker injured in Field A",
                            Severity = 2
                        },
                        new
                        {
                            Id = 9,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 25, 13, 537, DateTimeKind.Utc).AddTicks(9825),
                            EmergencyType = 7,
                            Latitude = 27.820553,
                            LocationDescription = "Farm Entrance",
                            Longitude = 31.802498,
                            Message = "Unusual activity reported near the farm entrance",
                            Severity = 0
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.DashboardModels.Equipment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<double>("Battery")
                        .HasColumnType("float");

                    b.Property<int>("Connectivity")
                        .HasColumnType("int");

                    b.Property<string>("EquipmentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("HoursUsed")
                        .HasColumnType("float");

                    b.Property<DateTime>("LastMaintenance")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Equipments");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Battery = 85.0,
                            Connectivity = 0,
                            EquipmentId = "EQ-001",
                            HoursUsed = 120.0,
                            LastMaintenance = new DateTime(2025, 5, 27, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9594),
                            Name = "Tractor A",
                            Status = 0
                        },
                        new
                        {
                            Id = 2,
                            Battery = 60.0,
                            Connectivity = 1,
                            EquipmentId = "EQ-002",
                            HoursUsed = 50.0,
                            LastMaintenance = new DateTime(2025, 6, 11, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9609),
                            Name = "Drone B",
                            Status = 2
                        },
                        new
                        {
                            Id = 3,
                            Battery = 95.0,
                            Connectivity = 0,
                            EquipmentId = "EQ-003",
                            HoursUsed = 30.0,
                            LastMaintenance = new DateTime(2025, 6, 16, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9610),
                            Name = "Sprinkler C",
                            Status = 1
                        },
                        new
                        {
                            Id = 4,
                            Battery = 75.0,
                            Connectivity = 0,
                            EquipmentId = "EQ-004",
                            HoursUsed = 200.0,
                            LastMaintenance = new DateTime(2025, 5, 12, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9611),
                            Name = "Harvester D",
                            Status = 0
                        },
                        new
                        {
                            Id = 5,
                            Battery = 100.0,
                            Connectivity = 1,
                            EquipmentId = "EQ-005",
                            HoursUsed = 20.0,
                            LastMaintenance = new DateTime(2025, 6, 21, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9613),
                            Name = "Seeder E",
                            Status = 2
                        },
                        new
                        {
                            Id = 6,
                            Battery = 50.0,
                            Connectivity = 0,
                            EquipmentId = "EQ-006",
                            HoursUsed = 90.0,
                            LastMaintenance = new DateTime(2025, 6, 6, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9664),
                            Name = "Plow F",
                            Status = 1
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.DashboardModels.Field", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Crop")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("HarvestDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Irrigation")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PlantingDate")
                        .HasColumnType("datetime2");

                    b.Property<double>("Size")
                        .HasColumnType("float");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Fields");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Crop = "Wheat",
                            HarvestDate = new DateTime(2025, 8, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9710),
                            Irrigation = 1,
                            Name = "Field Alpha",
                            PlantingDate = new DateTime(2025, 3, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9700),
                            Size = 10.5,
                            Status = 2
                        },
                        new
                        {
                            Id = 2,
                            Crop = "Corn",
                            HarvestDate = new DateTime(2025, 9, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9712),
                            Irrigation = 2,
                            Name = "Field Beta",
                            PlantingDate = new DateTime(2025, 4, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9712),
                            Size = 15.199999999999999,
                            Status = 2
                        },
                        new
                        {
                            Id = 3,
                            Crop = "Rice",
                            HarvestDate = new DateTime(2025, 7, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9714),
                            Irrigation = 3,
                            Name = "Field Gamma",
                            PlantingDate = new DateTime(2025, 2, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9714),
                            Size = 8.0,
                            Status = 2
                        },
                        new
                        {
                            Id = 4,
                            Crop = "Soybeans",
                            HarvestDate = new DateTime(2025, 11, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9716),
                            Irrigation = 1,
                            Name = "Field Delta",
                            PlantingDate = new DateTime(2025, 5, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9716),
                            Size = 12.699999999999999,
                            Status = 4
                        },
                        new
                        {
                            Id = 5,
                            Crop = "Barley",
                            HarvestDate = new DateTime(2025, 9, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9718),
                            Irrigation = 5,
                            Name = "Field Epsilon",
                            PlantingDate = new DateTime(2025, 1, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9718),
                            Size = 20.300000000000001,
                            Status = 2
                        },
                        new
                        {
                            Id = 6,
                            Crop = "Oats",
                            HarvestDate = new DateTime(2025, 10, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9723),
                            Irrigation = 4,
                            Name = "Field Zeta",
                            PlantingDate = new DateTime(2024, 12, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9719),
                            Size = 9.5,
                            Status = 3
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.DashboardModels.SoilMoisture", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("FieldId")
                        .HasColumnType("int");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Moisture")
                        .HasColumnType("int");

                    b.Property<int>("Optimal")
                        .HasColumnType("int");

                    b.Property<float>("PH")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("FieldId");

                    b.ToTable("SoilMoistures");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            FieldId = 1,
                            FieldName = "Field Alpha",
                            Moisture = 58,
                            Optimal = 65,
                            PH = 6.2f
                        },
                        new
                        {
                            Id = 2,
                            FieldId = 2,
                            FieldName = "Field Beta",
                            Moisture = 62,
                            Optimal = 60,
                            PH = 6.5f
                        },
                        new
                        {
                            Id = 3,
                            FieldId = 3,
                            FieldName = "Field Gamma",
                            Moisture = 70,
                            Optimal = 68,
                            PH = 6.8f
                        },
                        new
                        {
                            Id = 4,
                            FieldId = 4,
                            FieldName = "Field Delta",
                            Moisture = 45,
                            Optimal = 60,
                            PH = 5.9f
                        },
                        new
                        {
                            Id = 5,
                            FieldId = 5,
                            FieldName = "Field Epsilon",
                            Moisture = 67,
                            Optimal = 70,
                            PH = 6.3f
                        },
                        new
                        {
                            Id = 6,
                            FieldId = 6,
                            FieldName = "Field Zeta",
                            Moisture = 52,
                            Optimal = 60,
                            PH = 6f
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.DashboardModels.WeatherData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Humidity")
                        .HasColumnType("float");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Pressure")
                        .HasColumnType("float");

                    b.Property<double>("Temperature")
                        .HasColumnType("float");

                    b.Property<double>("UvIndex")
                        .HasColumnType("float");

                    b.Property<double>("WindSpeed")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.ToTable("WeatherDatas");
                });

            modelBuilder.Entity("Croppilot.Date.Models.DashboardModels.WeatherForecast", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("Day")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("High")
                        .HasColumnType("float");

                    b.Property<int>("Humidity")
                        .HasColumnType("int");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Low")
                        .HasColumnType("float");

                    b.Property<double>("Precipitation")
                        .HasColumnType("float");

                    b.Property<int>("Pressure")
                        .HasColumnType("int");

                    b.Property<double>("Wind")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.ToTable("WeatherForecasts");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Leasing", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LeasingDetails")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartingDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("Leasings");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Order", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("ShippingAddress")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Orders", (string)null);
                });

            modelBuilder.Entity("Croppilot.Date.Models.OrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("OrderId")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("OrderItems", (string)null);
                });

            modelBuilder.Entity("Croppilot.Date.Models.Post", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("VoteCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Posts");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("Availability")
                        .HasColumnType("int");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CuponId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CuponId");

                    b.HasIndex("UserId");

                    b.ToTable("Products");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Availability = 0,
                            CategoryId = 1,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9357),
                            Description = "Fresh vine-ripened tomatoes",
                            Name = "Organic Tomatoes",
                            Price = 19.99m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9357),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 2,
                            Availability = 0,
                            CategoryId = 1,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9422),
                            Description = "Crisp and refreshing cucumbers",
                            Name = "Cucumbers",
                            Price = 12.50m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9423),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 3,
                            Availability = 1,
                            CategoryId = 1,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9425),
                            Description = "Mixed color sweet peppers",
                            Name = "Bell Peppers",
                            Price = 18.75m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9425),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 4,
                            Availability = 0,
                            CategoryId = 2,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9427),
                            Description = "Sweet and juicy strawberries",
                            Name = "Strawberries",
                            Price = 25.99m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9428),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 5,
                            Availability = 0,
                            CategoryId = 2,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9430),
                            Description = "Premium imported mangoes",
                            Name = "Mangoes",
                            Price = 30.50m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9430),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 6,
                            Availability = 0,
                            CategoryId = 2,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9432),
                            Description = "Large sweet watermelons",
                            Name = "Watermelons",
                            Price = 45.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9433),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 7,
                            Availability = 0,
                            CategoryId = 3,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9435),
                            Description = "Whole milk 1L bottle",
                            Name = "Farm Fresh Milk",
                            Price = 20.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9435),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 8,
                            Availability = 0,
                            CategoryId = 3,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9437),
                            Description = "Aged cheddar cheese 200g",
                            Name = "Artisan Cheese",
                            Price = 35.75m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9438),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 9,
                            Availability = 0,
                            CategoryId = 3,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9440),
                            Description = "Natural Yogurt",
                            Name = "Natural Yogurt",
                            Price = 18.50m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9440),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 10,
                            Availability = 0,
                            CategoryId = 4,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9442),
                            Description = "Large brown eggs",
                            Name = "Free-Range Eggs (12pk)",
                            Price = 30.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9443),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 11,
                            Availability = 0,
                            CategoryId = 5,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9445),
                            Description = "Red rose plant in 12\" pot",
                            Name = "Rose Bush",
                            Price = 120.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9445),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 12,
                            Availability = 0,
                            CategoryId = 5,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9447),
                            Description = "Fragrant lavender for gardens",
                            Name = "Lavender Plant",
                            Price = 85.50m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9448),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 13,
                            Availability = 0,
                            CategoryId = 6,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9450),
                            Description = "Early girl tomato plants",
                            Name = "Tomato Seedlings",
                            Price = 15.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9450),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 14,
                            Availability = 0,
                            CategoryId = 6,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9452),
                            Description = "Burpless cucumber plants, disease-resistant",
                            Name = "Cucumber Seedlings",
                            Price = 13.25m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9453),
                            UserId = "642b8bd1-a65f-4598-95bc-29b833dcb84e"
                        },
                        new
                        {
                            Id = 15,
                            Availability = 0,
                            CategoryId = 7,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9455),
                            Description = "Organic chicken feed",
                            Name = "Poultry Feed 20kg",
                            Price = 150.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9455),
                            UserId = "655501be-8ca7-434d-9cbe-6e8d23b3d92c"
                        },
                        new
                        {
                            Id = 16,
                            Availability = 0,
                            CategoryId = 7,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9457),
                            Description = "Nutritional cattle mix",
                            Name = "Cattle Feed 25kg",
                            Price = 220.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9457),
                            UserId = "655501be-8ca7-434d-9cbe-6e8d23b3d92c"
                        },
                        new
                        {
                            Id = 17,
                            Availability = 0,
                            CategoryId = 8,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9459),
                            Description = "Professional grade shears",
                            Name = "Pruning Shears",
                            Price = 65.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9460),
                            UserId = "655501be-8ca7-434d-9cbe-6e8d23b3d92c"
                        },
                        new
                        {
                            Id = 18,
                            Availability = 0,
                            CategoryId = 8,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9462),
                            Description = "Sturdy steel garden hoe",
                            Name = "Garden Hoe",
                            Price = 45.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9462),
                            UserId = "655501be-8ca7-434d-9cbe-6e8d23b3d92c"
                        },
                        new
                        {
                            Id = 19,
                            Availability = 0,
                            CategoryId = 10,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9464),
                            Description = "Nutrient-rich compost",
                            Name = "Compost 10kg",
                            Price = 40.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9465),
                            UserId = "655501be-8ca7-434d-9cbe-6e8d23b3d92c"
                        },
                        new
                        {
                            Id = 20,
                            Availability = 0,
                            CategoryId = 10,
                            CreatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9467),
                            Description = "Organic soil amendment",
                            Name = "Worm Castings",
                            Price = 55.00m,
                            UpdatedAt = new DateTime(2025, 6, 26, 12, 27, 13, 537, DateTimeKind.Utc).AddTicks(9467),
                            UserId = "655501be-8ca7-434d-9cbe-6e8d23b3d92c"
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.ProductImage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductImages");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Organic Tomatoes_2520f86d-8700-4b06-bed5-e9e317e71d95_R %283%29.jpg",
                            ProductId = 1
                        },
                        new
                        {
                            Id = 2,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Cucumbers_d29f0202-cbf9-4f0e-ae62-344521eec15c_R %284%29.jpg",
                            ProductId = 2
                        },
                        new
                        {
                            Id = 3,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Cucumbers_771c182f-18c0-43b4-af95-281406ed89fe_OIP.jpg",
                            ProductId = 2
                        },
                        new
                        {
                            Id = 4,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Bell Peppers_765ca4be-6179-4ce9-bed6-57613481d475_OIP %281%29.jpg",
                            ProductId = 3
                        },
                        new
                        {
                            Id = 5,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Bell Peppers_7d5babea-020f-4e79-8552-9450f997853e_primary-430.jpg",
                            ProductId = 3
                        },
                        new
                        {
                            Id = 6,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Bell Peppers_d561a4c3-5e47-4ec4-9a40-dccc6b7157bc_Bell-Peppers.jpg",
                            ProductId = 3
                        },
                        new
                        {
                            Id = 7,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Farm Fresh Milk_b66be9e1-5421-4592-9038-a546c2c49bd6_R %285%29.jpg",
                            ProductId = 7
                        },
                        new
                        {
                            Id = 8,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Farm Fresh Milk_affe0646-14b5-4440-9373-823c0aab4132_R %286%29.jpg",
                            ProductId = 7
                        },
                        new
                        {
                            Id = 9,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Farm Fresh Milk_5d1f9c4f-444c-49d3-8cb0-eb4e1ff19d40_R %287%29.jpg",
                            ProductId = 7
                        },
                        new
                        {
                            Id = 10,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Strawberries_eefc35b5-e857-4cf5-a5a3-b09fa75f3c6f_R %288%29.jpg",
                            ProductId = 4
                        },
                        new
                        {
                            Id = 11,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Strawberries_779eca40-dfb3-4c51-9ae4-f6d022bdea1a_R %289%29.jpg",
                            ProductId = 4
                        },
                        new
                        {
                            Id = 12,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Mangoes_d0c89732-4204-4804-ba7c-d0ef0a822573_download.jpg",
                            ProductId = 5
                        },
                        new
                        {
                            Id = 13,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Mangoes_0aa7f942-c40f-4f0f-a2d0-f960d599a9d2_OIP %282%29.jpg",
                            ProductId = 5
                        },
                        new
                        {
                            Id = 14,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Watermelons_f13f305a-8f7d-4bdd-ad67-08982d51bd88_OIP %283%29.jpg",
                            ProductId = 6
                        },
                        new
                        {
                            Id = 15,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Watermelons_5a90c66a-c7c8-4de4-8ae3-c5d763f63955_OIP %284%29.jpg",
                            ProductId = 6
                        },
                        new
                        {
                            Id = 16,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Artisan Cheese_a1d3e7ec-afba-4883-812d-30216c3cb4e7_R %2810%29.jpg",
                            ProductId = 8
                        },
                        new
                        {
                            Id = 17,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Artisan Cheese_26e0102e-b43e-4124-b3af-9d87e6ad5ce5_OIP %285%29.jpg",
                            ProductId = 8
                        },
                        new
                        {
                            Id = 18,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Natural Yogurt_c5f98e11-0931-43e2-9d9a-fd70f42a5dbd_OIP %286%29.jpg",
                            ProductId = 9
                        },
                        new
                        {
                            Id = 19,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Natural Yogurt_7337ee02-8893-4ab2-aec7-cb1414df80dd_R %2811%29.jpg",
                            ProductId = 9
                        },
                        new
                        {
                            Id = 20,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Free-Range Eggs %2812pk%29_2d35c5fc-6d4c-4f8e-aa13-1c16ed674601_OIP %287%29.jpg",
                            ProductId = 10
                        },
                        new
                        {
                            Id = 21,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Free-Range Eggs %2812pk%29_33b205d9-91da-40ef-9a96-c9a0337b3fe4_OIP %288%29.jpg",
                            ProductId = 10
                        },
                        new
                        {
                            Id = 22,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Rose Bush_c7bff713-4c6c-4352-869e-c85f673baf1f_R %2812%29.jpg",
                            ProductId = 11
                        },
                        new
                        {
                            Id = 23,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Rose Bush_edb3d662-5727-4c71-93a5-bb460cddd3c3_OIP %289%29.jpg",
                            ProductId = 11
                        },
                        new
                        {
                            Id = 24,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Lavender Plant_53ef2c19-0a3b-4114-b178-32c1a6110069_OIP %2810%29.jpg",
                            ProductId = 12
                        },
                        new
                        {
                            Id = 25,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Tomato Seedlings_e6e9622b-bbc1-4aa6-96b2-297ebb8257f4_IMG_9275.jpg",
                            ProductId = 13
                        },
                        new
                        {
                            Id = 26,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Cucumber Seedlings_89f422cb-7093-4814-bd2f-0d4ad099cc6e_R %2813%29.jpg",
                            ProductId = 14
                        },
                        new
                        {
                            Id = 27,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Poultry Feed 20kg_e9e91afb-1a50-4726-bd37-e162ec9ed15a_OIP %2811%29.jpg",
                            ProductId = 15
                        },
                        new
                        {
                            Id = 28,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Poultry Feed 20kg_2a70c24b-9b7a-4905-af6e-796ab5566709_OIP %2812%29.jpg",
                            ProductId = 15
                        },
                        new
                        {
                            Id = 29,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Cattle Feed 25kg_c8ec9374-4839-4004-96c1-0054ad698925_OIP %2813%29.jpg",
                            ProductId = 16
                        },
                        new
                        {
                            Id = 30,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Cattle Feed 25kg_b441f3a3-b774-4efb-bf75-543fa2700a4e_OIP %2814%29.jpg",
                            ProductId = 16
                        },
                        new
                        {
                            Id = 31,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Pruning Shears_e40f5237-442d-4452-8f32-2883bcfbe8bf_OIP %2815%29.jpg",
                            ProductId = 17
                        },
                        new
                        {
                            Id = 32,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Pruning Shears_54af077d-62db-4fe4-9bcb-ad0fbf184d15_OIP %2816%29.jpg",
                            ProductId = 17
                        },
                        new
                        {
                            Id = 33,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Garden Hoe_477a31d4-f9c0-4ec2-bd8f-bc81c5aaeaac_OIP %2817%29.jpg",
                            ProductId = 18
                        },
                        new
                        {
                            Id = 34,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Compost 10kg_d8e29f75-83a8-4968-992f-343303f0404b_61gvcvZgosL._AC_SL1500_.jpg",
                            ProductId = 19
                        },
                        new
                        {
                            Id = 35,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Compost 10kg_a02227ba-a1a8-4053-9f95-7c354402c179_R %2814%29.jpg",
                            ProductId = 19
                        },
                        new
                        {
                            Id = 36,
                            ImageUrl = "https://graduationprojetct.blob.core.windows.net/product-images/Worm Castings_e1155a6c-3ce2-4d69-979f-a8840c282f02_OIP %2818%29.jpg",
                            ProductId = 20
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.Review", b =>
                {
                    b.Property<int>("ReviewID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ReviewID"));

                    b.Property<string>("Headline")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("ProductID")
                        .HasColumnType("int");

                    b.Property<decimal>("Rating")
                        .HasColumnType("decimal(3,2)");

                    b.Property<DateTime>("ReviewDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("ReviewText")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("ReviewID");

                    b.HasIndex("ProductID");

                    b.HasIndex("UserID");

                    b.ToTable("Reviews", t =>
                        {
                            t.HasCheckConstraint("CK_Review_Rating", "[Rating] BETWEEN 1.0 AND 5.0");
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.Rover", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Rovers");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Vote", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("TargetId")
                        .HasColumnType("int");

                    b.Property<string>("TargetType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("VoteType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId", "TargetId", "TargetType")
                        .IsUnique();

                    b.ToTable("Votes", t =>
                        {
                            t.HasCheckConstraint("CK_Vote_TargetType", "[TargetType] IN ('post', 'comment')");

                            t.HasCheckConstraint("CK_Vote_VoteType", "[VoteType] IN (1, -1)");
                        });
                });

            modelBuilder.Entity("Croppilot.Date.Models.Wishlist", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("Wishlists");
                });

            modelBuilder.Entity("Croppilot.Date.Models.WishlistItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("WishlistId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("WishlistId");

                    b.ToTable("WishlistItems");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Croppilot.Date.Identity.RefreshToken", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.AiModel.FeedbackEntry", b =>
                {
                    b.HasOne("Croppilot.Date.Models.AiModel.ModelResult", "ModelResult")
                        .WithMany("FeedbackEntries")
                        .HasForeignKey("ModelResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ModelResult");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Cart", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.CartItem", b =>
                {
                    b.HasOne("Croppilot.Date.Models.Cart", "Cart")
                        .WithMany("CartItems")
                        .HasForeignKey("CartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Croppilot.Date.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Cart");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Croppilot.Date.Models.ChatHistory", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany("chatHistories")
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Comment", b =>
                {
                    b.HasOne("Croppilot.Date.Models.Comment", "ParentComment")
                        .WithMany("Replies")
                        .HasForeignKey("ParentCommentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Croppilot.Date.Models.Post", "Post")
                        .WithMany("Comments")
                        .HasForeignKey("PostId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ParentComment");

                    b.Navigation("Post");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Cupon", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany("Cupons")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.DashboardModels.SoilMoisture", b =>
                {
                    b.HasOne("Croppilot.Date.Models.DashboardModels.Field", "Field")
                        .WithMany("SoilMoistureReadings")
                        .HasForeignKey("FieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Field");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Leasing", b =>
                {
                    b.HasOne("Croppilot.Date.Models.Product", "Product")
                        .WithMany("Leasings")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Order", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.OrderItem", b =>
                {
                    b.HasOne("Croppilot.Date.Models.Order", "Order")
                        .WithMany("OrderItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Post", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Product", b =>
                {
                    b.HasOne("Croppilot.Date.Models.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Croppilot.Date.Models.Cupon", "Cupon")
                        .WithMany("Products")
                        .HasForeignKey("CuponId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany("Products")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Cupon");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.ProductImage", b =>
                {
                    b.HasOne("Croppilot.Date.Models.Product", "Product")
                        .WithMany("ProductImages")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Review", b =>
                {
                    b.HasOne("Croppilot.Date.Models.Product", "Product")
                        .WithMany("Reviews")
                        .HasForeignKey("ProductID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Rover", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Vote", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Wishlist", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", "User")
                        .WithOne("Wishlist")
                        .HasForeignKey("Croppilot.Date.Models.Wishlist", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Croppilot.Date.Models.WishlistItem", b =>
                {
                    b.HasOne("Croppilot.Date.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Croppilot.Date.Models.Wishlist", "Wishlist")
                        .WithMany("WishlistItems")
                        .HasForeignKey("WishlistId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Wishlist");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Croppilot.Date.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Croppilot.Date.Identity.ApplicationUser", b =>
                {
                    b.Navigation("Cupons");

                    b.Navigation("Products");

                    b.Navigation("RefreshTokens");

                    b.Navigation("Wishlist");

                    b.Navigation("chatHistories");
                });

            modelBuilder.Entity("Croppilot.Date.Models.AiModel.ModelResult", b =>
                {
                    b.Navigation("FeedbackEntries");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Cart", b =>
                {
                    b.Navigation("CartItems");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Category", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Comment", b =>
                {
                    b.Navigation("Replies");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Cupon", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("Croppilot.Date.Models.DashboardModels.Field", b =>
                {
                    b.Navigation("SoilMoistureReadings");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Order", b =>
                {
                    b.Navigation("OrderItems");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Post", b =>
                {
                    b.Navigation("Comments");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Product", b =>
                {
                    b.Navigation("Leasings");

                    b.Navigation("ProductImages");

                    b.Navigation("Reviews");
                });

            modelBuilder.Entity("Croppilot.Date.Models.Wishlist", b =>
                {
                    b.Navigation("WishlistItems");
                });
#pragma warning restore 612, 618
        }
    }
}

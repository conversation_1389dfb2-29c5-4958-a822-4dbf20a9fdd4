﻿/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Bases/Error.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Bases/HttpStatusCodeConverter.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Bases/Response.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Bases/ResponseHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Beheviors/ValidtorBehevior.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/AIModels/Handlers/AIModelHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/AIModels/Models/GetCurrentValue.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/AIModels/Models/GetFeedback.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/AIModels/Models/GetWateringFeedback.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/AIModels/Models/PredictModelCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/AIModels/Results/CurrentValueResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/AIModels/Results/FeedbackResukt.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/AIModels/Results/ModelResults.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/AIModels/Results/PredictionResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/AddUserCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/ChangeUserPasswordCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/ConfirmEmailHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/ForgetPasswordHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/LoginWithThirdPartyHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/RefreshTokenCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/RegisterWithExternalHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/ResetPasswordUsingOTPHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/RevokeRefreshTokenCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Handlers/SignInCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/AddUserCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/ChangeUserPasswordCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/ConfirmEmailCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/ForgetPasswordCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/ForgetPasswordUsingOTPCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/LoginWithExternalCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/RefreshTokenCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/RegisterWithExternalCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/ResendConfirmEmailCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/ResetPasswordCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/RevokeRefreshTokenCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Models/SignInCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Result/SignInResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Validators/AddUserValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Validators/ChangeUserPasswordValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Validators/ConfirmEmailValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Validators/ForgetPasswordUsingOTPValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Validators/ForgetPasswordValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Validators/ResendConfirmEmailValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Validators/ResetPasswordValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Commands/Validators/SignInValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Queries/Handlers/GetCurrentUserIdQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Queries/Handlers/ResetPasswordQueryHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Queries/Models/GetCurrentUserIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Queries/Models/ResetPasswordUsingOTPQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Bases/AuthorizationHandlerBase.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Handlers/AddRoleCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Handlers/AssignRolesToUserCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Handlers/DeleteRoleCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Handlers/EditRoleCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Models/AddRoleCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Models/AssignRolesToUserCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Models/DeleteRoleCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Models/EditRoleCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Validators/AddRoleCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Validators/AssignRolesToUserCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Commands/Validators/EditRoleCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Queries/Handlers/GetRoleByIdQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Queries/Handlers/GetRoleByNameQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Queries/Handlers/GetRoleListQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Queries/Models/GetRoleByIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Queries/Models/GetRoleByNameQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Queries/Models/GetRolesListQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authorization/Queries/Result/GetRole.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Command/Handlers/CartCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Command/Models/AddProductToCartCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Command/Models/RemoveProductFromCartCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Command/Validators/AddProductToCartCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Command/Validators/RemoveProductFromCartCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Query/Handlers/CartQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Query/Models/GetCartQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Query/Result/GetCartItemResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Query/Result/GetCartResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Carts/Query/Validators/GetCartQueryValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Command/Handlers/CategoryCommandHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Command/Models/AddCategoryCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Command/Models/DeleteCategoryCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Command/Models/EditCategoryCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Command/Validtators/AddCategoryValidtor.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Command/Validtators/EditCategoryValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Query/Handlers/CategoryHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Query/Models/GetAllCategoryQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Query/Models/GetCategoryByIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Query/Models/GetCategoryPaginatedQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Query/Result/GetAllCategoryResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Query/Result/GetCategoryByIdResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Category/Query/Result/GetCategoryPaginatedResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/ChatBot/Command/Handlers/GetChatResponseHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/ChatBot/Command/Models/MessageRequestModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/ChatBot/Query/Handlers/ChatHistoryQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/ChatBot/Query/Models/GetChatHistory.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/ChatBot/Query/Result/GetChatHistoryResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Command/Handlers/CommentCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Command/Models/AddCommentCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Command/Models/DeleteCommentCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Command/Models/UpdateCommentCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Command/Validators/AddCommentCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Command/Validators/DeleteCommentCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Command/Validators/UpdateCommentCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Query/Handlers/CommentQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Query/Models/GetCommentByIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Query/Models/GetCommentsByPostQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Comments/Query/Result/CommentResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/CosmosDb/Handlers/CosmseDbHnadlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/CosmosDb/Models/AllReadingRequest.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/CosmosDb/Models/GetIoTData.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/CosmosDb/Models/GetReadingByDevice.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/CosmosDb/Models/ReadingRequest.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/CosmosDb/Result/GetIotDataResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Cupon/Command/Handlers/AssignToProductCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Cupon/Command/Handlers/CreateCuponCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Cupon/Command/Models/AssignToProductCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Cupon/Command/Models/CreateCuponCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Cupon/Command/Validators/AssignToProductCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Cupon/Command/Validators/CreateCuponCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Alerts/AlertHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Alerts/Models/CreateAlert.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Alerts/Models/GetAllAlerts.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Alerts/Result/GetAllAlertsResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Equipment/EquipmentHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Equipment/Models/CreateEquipmentModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Equipment/Models/DeleteEquipmentModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Equipment/Models/GetAllEquipmentModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Equipment/Models/UpdateEquipmentModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Equipment/Models/UpdateEquipmentStatusModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Equipment/Result/GetAllEquipmentResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/FarmStatues/FarmHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/FarmStatues/FarmStatusResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/FarmStatues/GetFarmStatusModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/FarmStatus/Soil/SoilHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/FarmStatus/Soil/SoilModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Field/FieldHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Field/Models/CreateFieldModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Field/Models/DeleteFieldModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Field/Models/GetAllFieldModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Field/Models/GetFieldById.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Field/Models/UpdateFieldModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Field/Results/GetAllFieldResults.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Field/Results/GetFieldByIdResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Soil/SoilHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Soil/SoilMoistureModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Soil/SoilResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Weather/Handlers/WeatherHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Weather/Models/WeatherDataModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Weather/Models/WeatherForcastModel.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Weather/Results/WeatherDataResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Dashbored/Weather/Results/WeatherForcastResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Command/Handler/LeasingCommandHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Command/Model/DeleteLeaseCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Command/Model/EndLeaseCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Command/Model/LeaseProductCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Query/Handlers/LeasingHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Query/Models/GetActiveLeasesQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Query/Models/GetAllLeasingsQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Query/Models/GetLeasingByIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Query/Models/GetLeasingsByProductIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Query/Result/GetAllActiveLeasingResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Leasing/Query/Result/GetAllLeasingResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Notification/Handlers/NotificationHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Notification/Models/PushbulletRequest.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Notification/Models/SmsRequest.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Handlers/OrderCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Models/CreateOrderCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Models/CreateOrderItemCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Models/DeleteOrderCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Models/UpdateOrderCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Result/CreateOrderResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Validators/CreateOrderCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Validators/CreateOrderItemCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Validators/DeleteOrderCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Command/Validators/EditOrderCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Query/Handlers/OrderQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Query/Models/GetAllOrdersQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Query/Models/GetOrderByIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Query/Models/GetUserOrdersQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Query/Result/GetOrderItemResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Orders/Query/Result/GetOrderResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Payment/Command/Handler/CreateCheckOutSessionCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Payment/Command/Model/CreateCheckOutSessionCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Command/Handlers/PostCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Command/Models/AddPostCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Command/Models/DeletePostCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Command/Models/UpdatePostCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Command/Validators/AddPostCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Command/Validators/DeletePostCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Command/Validators/UpdatePostCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Query/Handlers/PostQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Query/Models/GetPostByIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Query/Models/GetPostsQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Query/Result/PostResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Posts/Query/Validators/GetPostByIdQueryValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Command/Handlers/ProductCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Command/Models/AddProductCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Command/Models/DeleteProductCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Command/Models/EditProductCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Command/Validators/AddProductCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Command/Validators/DeleteProductCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Command/Validators/EditProductCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Query/Handlers/ProductHandlers.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Query/Models/GetAllProductQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Query/Models/GetProductByIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Query/Models/GetProductPaginatedQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Query/Result/GetAllProductResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Query/Result/GetProductByIdResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Query/Result/GetProductPaginatedResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Product/Query/Validators/GetProductPaginatedQueryValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Command/Handlers/ReviewCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Command/Models/AddReviewCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Command/Models/DeleteReviewCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Command/Models/UpdateReviewCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Command/Validators/AddReviewCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Command/Validators/DeleteReviewCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Command/Validators/UpdateReviewCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Query/Handlers/ReviewQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Query/Models/GetReviewsByProductQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Query/Result/ReviewResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Reviews/Query/Validators/GetReviewsByProductQueryValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Handlers/ChangeUserImageCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Handlers/ChangeUserRoleCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Handlers/CheckUserValidCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Handlers/DeleteUserCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Handlers/EditUserCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Handlers/RemoveUserFromRoleCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Models/ChangeUserImageCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Models/ChangeUserRoleCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Models/CheckUserValidCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Models/DeleteUserCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Models/EditUserCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Models/RemoveUserFromRoleCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Validators/ChangeUserImageValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Validators/ChangeUserRoleCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Validators/CheckUserValidCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Validators/EditUserCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Commands/Validators/RemoveUserFromRoleCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Handlers/GetUserByIdQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Handlers/GetUserByUserNameQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Handlers/GetUserPaginatedQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Handlers/GetUserRolesQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Handlers/GetUsersAssignedToRoleQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Models/GetUserByIdQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Models/GetUserByUserNameQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Models/GetUserPaginatedQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Models/GetUserRolesQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Models/GetUsersAssignedToRoleQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Result/GetUser.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Result/GetUserAssignedToRoleResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/User/Queries/Result/GetUserRoleResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Votes/Command/Handlers/VoteCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Votes/Command/Models/AddOrUpdateVoteCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Votes/Command/Models/DeleteVoteCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Votes/Command/Validators/AddOrUpdateVoteCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Votes/Command/Validators/DeleteVoteCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Command/Handlers/WishlistCommandHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Command/Models/AddProductToWishlistCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Command/Models/RemoveProductFromWishlistCommand.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Command/Validators/AddProductToWishlistCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Command/Validators/RemoveProductFromWishlistCommandValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Query/Handlers/WishlistQueryHandler.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Query/Models/GetWishlistQuery.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Query/Result/GetWishlistItemResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Query/Result/GetWishlistResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/WishLists/Query/Validators/GetWishlistQueryValidator.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/GlobalUsing.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Authentication/AuthenticationMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Authorization/AuthorizationMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Authorization/Query/GetRoleMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Carts/CartQueryMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Categories/CategoryQueryMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Cupon/Command/CreateCupponMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Cupon/CuponMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Orders/OrderCommandMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Orders/OrderQueryMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Product/ProductCommandMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Product/ProductQueryMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/User/Query/GetUserAssignedToRoleMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/User/Query/GetUserMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/User/UserMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Wishlists/WishlistQueryMapping.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Middlerware/ErrorHandlerMiddleware.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/ModelCoreDependencies.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Features/Authentication/Queries/Result/deletme.txt
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Core/Mapping/Authentication/Deleteme.txt

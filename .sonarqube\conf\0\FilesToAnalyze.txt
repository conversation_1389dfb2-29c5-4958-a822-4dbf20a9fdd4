﻿/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Constants/RateLimiters.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/DTOS/EmailSendDto.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/DTOS/ExternalAuthUserDTO.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/DTOS/FacebookResultDto.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/DTOS/Product/CreateProductDTO.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/DTOS/Product/GetProductDTO.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/DTOS/Product/UpdateProductDTO.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/DTOS/TokenDto.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Enum/Availability.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Enum/CategoryOrderingEnum.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Enum/Discount_Type.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Enum/OperationResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Enum/OrderStatus.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Enum/ProductOrderingEnum.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Enum/UserRoleEnum.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/GlobalUsing.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/Enum/EmergencyType.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/Enum/EquipmentConnectivity.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/Enum/EquipmentStatus.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/Enum/FieldStatus.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/Enum/IrrigationType.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/Enum/SeverityType.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/FarmStatusDto.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/SoilGridsResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/SoilProperty.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/Dashboard/SoilQualityReport.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/GoogleTokenInfoResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/JwtSettings.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/SmartFarmObservation.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/StripeSettings.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Helpers/TokenResponse.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Identity/ApplicationRole.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Identity/ApplicationUser.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Identity/RefreshToken.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/AiModel/FeedbackEntry.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/AiModel/ModelResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Cart.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/CartItem.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Category.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/ChatHistory.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Comment.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Cupon.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/DashboardModels/Alert.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/DashboardModels/CropYield.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/DashboardModels/Equipment.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/DashboardModels/FarmerAdminDashboard.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/DashboardModels/Field.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/DashboardModels/SoilMoisture.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/DashboardModels/WeatherData.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/DashboardModels/WeatherForecast.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Leasing.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Order.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/OrderItem.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Post.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Product.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/ProductImage.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Review.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Vote.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/Wishlist.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Date/Models/WishlistItem.cs

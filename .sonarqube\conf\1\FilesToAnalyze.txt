﻿/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Comman/PaginatedResult.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Comman/QueryableExtensions.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Comman/SD.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Comman/SeedUserOptions.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/AiModelConfigration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/CartConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/CartItemConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/CategoryConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/CommentConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/CuponConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/LeasingConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/OrderConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/OrderItemConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/PostConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/ProductConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/ProductImageConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/ReviewConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/VoteConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/WishlistConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Configuration/WishlistItemConfiguration.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Data/AppDbContext.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Data/Extenstions/ModelBuilderExtensions.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Data/SeedData/AlertSeed.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Data/SeedData/CategorySeed.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Data/SeedData/EquipmentSeed.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Data/SeedData/FieldSeed.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Data/SeedData/ProductImageSeed.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Data/SeedData/ProductSeed.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Data/SeedData/SoilMoistureSeed.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Extensions/UserExtensions.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Generics/Implementation/GenericRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Generics/Interfaces/IGenericRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/GlobalUsing.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/HealthChecks/CustomHealthChecks/AzureBlobStorageHealthCheck.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/HealthChecks/CustomHealthChecks/MailHealthChecks.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/HealthChecks/CustomHealthChecks/OpenAIHealthCheck.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/HealthChecks/Extensions/HealthCheckExtensions.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/HealthChecks/Options/MailJetHealthCheckOptions.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Migrations/20250429213844_initial-new-database.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Migrations/20250429213844_initial-new-database.Designer.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Migrations/20250429214857_seeding-data.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Migrations/20250429214857_seeding-data.Designer.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Migrations/20250502173305_add isfavorite attribute.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Migrations/20250502173305_add isfavorite attribute.Designer.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Migrations/20250502191837_delete isfavorite attribute.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Migrations/20250502191837_delete isfavorite attribute.Designer.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Migrations/AppDbContextModelSnapshot.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/ModelInfrastructureDependencies.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/AiRepository/FeedbackRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/AiRepository/ModelRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/CartRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/CategoryRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/ChatRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/CommentRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/CuponRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/Dashbored/AlertsRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/Dashbored/EquipmentRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/Dashbored/FieldRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/Dashbored/SoilRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/Dashbored/WeatherDataRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/Dashbored/WeatherForcastRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/LeasingRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/OrderRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/PostRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/ProductImageRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/ProductRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/RefreshTokenRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/ReviewRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/UnitOfWork.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/VoteRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Implementation/WishlistRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/AiRepository/IFeedbackRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/AiRepository/IModelRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/Dashbored/IAlertsRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/Dashbored/IEquipmentRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/Dashbored/IFieldRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/Dashbored/ISoilRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/Dashbored/IWeatherDataRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/Dashbored/IWeatherForcastRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/ICartRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/ICategoryRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IChatRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/ICommentRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/ICuponRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/ILeasingRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IOrderRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IPostRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IProductImageRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IProductRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IRefreshTokenRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IReviewRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IUnitOfWork.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IVoteRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Repositories/Interfaces/IWishlistRepository.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Seeder/RoleSeeder.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Infrastructure/Seeder/UserSeeder.cs

﻿/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Bases/AppControllerBase.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/AIModelController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/AuthenticationController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/AuthorizationController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/CartController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/CategoryController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/ChatBotController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/CommentsController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/CuponController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/DashboredController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/IoTController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/LeasingController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/NotificationController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/OrderController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/PaymentController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/PostsController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/ProductController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/ReviewsController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/TestController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/UserController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/VotesController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/WebhookController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Controller/WishlistController.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/GlobalUsing.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/ModelApiDependencies.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Program.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/obj/Debug/net8.0/Croppilot.API.GlobalUsings.g.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/obj/Debug/net8.0/.NETCoreApp,Version=v8.0.AssemblyAttributes.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/obj/Debug/net8.0/Croppilot.API.AssemblyInfo.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/obj/Debug/net8.0/Croppilot.API.MvcApplicationPartsAssemblyInfo.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/appsettings.Development.json
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/appsettings.json
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/AiModels/best.onnx
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/AiModels/mobilenet_model.onnx
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/AiModels/Rl_Model.onnx
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/AppDbContext.dgml
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Croppilot.API.http
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/images/20250323_020813564.jpg
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Properties/launchSettings.json
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.API/Properties/ServiceDependencies/Crop-Pilot-Api - Web Deploy2/profile.arm.json

﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Comman\**" />
    <Compile Remove="Features\Votes\Query\**" />
    <EmbeddedResource Remove="Comman\**" />
    <EmbeddedResource Remove="Features\Votes\Query\**" />
    <None Remove="Comman\**" />
    <None Remove="Features\Votes\Query\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Croppilot.Date\Croppilot.Date.csproj" />
    <ProjectReference Include="..\Croppilot.Services\Croppilot.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="Mapster" Version="7.4.2-pre02" />
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.12" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Features\Comments\Query\Validators\" />
    <Folder Include="Features\Cupon\Query\" />
    <Folder Include="Features\Payment\Query\" />
    <Folder Include="Mapping\Authentication\Command\" />
    <Folder Include="Mapping\Authentication\Query\" />
    <Folder Include="Mapping\Authorization\Command\" />
    <Folder Include="Mapping\Cupon\Query\" />
    <Folder Include="Mapping\User\Command\" />
    <Folder Include="Features\Orders\Query\Validators\" />

  </ItemGroup>

</Project>

﻿/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/AiSerives/IChatService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/AiSerives/IModelServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/AiSerives/IRlPredictServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/DashboredServices/IAlertsServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/DashboredServices/IEquipmentService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/DashboredServices/IFarmStatusService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/DashboredServices/IFieldService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/DashboredServices/ISoilServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/DashboredServices/IWeatherServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/EmbbeddedServices/ICosmosDbService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IAuthenticationService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IAuthorizationService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IAzureBlobStorageService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/ICartService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/ICategoryService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/ICommentService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/ICuponService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IEmailService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IExternalAuthService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/ILeasingService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/INotificationServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IOrderService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IPaymentService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IPostService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IProductImageServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IProductServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IReviewService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IUserService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IVoteService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Abstract/IWishlistService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/GlobalUsing.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/ModelServicesDependencies.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/AIServises/ChatService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/AIServises/ModelServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/AIServises/RlPredictServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/AuthenticationService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/AuthorizationService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/AzureBlobStorageService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/CartService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/CategoryService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/CommentService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/CuponService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/DashboredServices/AlertsServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/DashboredServices/EquipmentService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/DashboredServices/FarmStatusService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/DashboredServices/FieldService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/DashboredServices/Helper/SoilMethods.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/DashboredServices/SoilServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/DashboredServices/WeatherServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/EmailService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/EmbbeddedSerivces/CosmosDbService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/ExternalAuthService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/LeasingService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/NotificationServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/OrderService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/PaymentService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/PostService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/ProductImageServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/ProductServices.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/ReviewService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/UserService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/VoteService.cs
/home/<USER>/Desktop/Graduation-Prroject/Back-End/Croppilot.Services/Services/WishlistService.cs

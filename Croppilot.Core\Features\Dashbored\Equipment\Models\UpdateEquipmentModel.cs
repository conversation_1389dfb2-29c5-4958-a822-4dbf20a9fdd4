﻿using Croppilot.Date.Helpers.Dashboard.Enum;

namespace Croppilot.Core.Features.Dashbored.Equipment.Models
{
    public class UpdateEquipmentModel : IRequest<Response<string>>
    {
        public string EquipmentId { get; set; }
        public string Name { get; set; } = string.Empty;
        public EquipmentStatus Status { get; set; }
        public DateTime LastMaintenance { get; set; } = DateTime.UtcNow;
        public double HoursUsed { get; set; }
        public double Battery { get; set; }
        public EquipmentConnectivity Connectivity { get; set; }
    }
}

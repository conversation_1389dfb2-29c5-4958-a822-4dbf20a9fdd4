﻿global using Microsoft.Extensions.Logging;
global using Microsoft.AspNetCore.Mvc;
global using FluentValidation;
global using MediatR;
global using Microsoft.AspNetCore.Http;
global using Mapster;
global using FluentValidation.AspNetCore;
global using MapsterMapper;

global using Croppilot.Core.Features.Product.Command.Models;
global using Croppilot.Core.Features.Product.Query.Result;
global using Croppilot.Date.Enum;
global using Croppilot.Core.Bases;
global using Croppilot.Core.Features.Orders.Query.Result;
global using Croppilot.Core.Features.Orders.Query.Models;
global using Croppilot.Core.Features.Carts.Query.Models;
global using Croppilot.Core.Features.Carts.Query.Result;
global using Croppilot.Services.Abstract;


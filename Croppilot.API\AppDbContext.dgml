﻿<?xml version="1.0" encoding="utf-8"?>
<DirectedGraph GraphDirection="LeftToRight" ZoomLevel="0.33" xmlns="http://schemas.microsoft.com/vs/2009/dgml">
  <Nodes>
    <Node Id="ApplicationRole" Category="EntityType" Annotations="Relational:Schema: Relational:TableName: AspNetRoles RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-750.512197553877,-79.7035620402623,233.20333597819,197.880444042969" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="ApplicationRole" Name="ApplicationRole" />
    <Node Id="ApplicationRole.ConcurrencyStamp" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-730.512151406931,72.2167690191029,160.52,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="True" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="ConcurrencyStamp (string)" MaxLength="None" Name="ConcurrencyStamp" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationRole.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-730.512223224965,-39.7034580316783,70.8833333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (string)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationRole.Name" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 256" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-629.628856729196,-39.7034580316783,92.3200000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="Name (string)" MaxLength="(256)" Name="Name" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationRole.NormalizedName" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 256" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-730.512219359405,16.2566249761342,153.526666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="True" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="NormalizedName (string)" MaxLength="(256)" Name="NormalizedName" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser" Category="EntityType" Annotations="Relational:Schema: Relational:TableName: AspNetUsers RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="545.431048518422,-191.623761353786,633.316981995736,421.721082440354" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="ApplicationUser" Name="ApplicationUser" />
    <Node Id="ApplicationUser.AccessFailedCount" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="565.431210409475,72.2169521245717,142.4,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="AccessFailedCount (int)" MaxLength="None" Name="AccessFailedCount" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="ApplicationUser.Address" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1017.46462919203,-151.623536309266,103.133333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="Address (string)" MaxLength="None" Name="Address" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.ConcurrencyStamp" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="565.431269003225,128.177035132384,160.52,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="True" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="ConcurrencyStamp (string)" MaxLength="None" Name="ConcurrencyStamp" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.Email" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 256" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="774.284414755179,-151.623536309266,88.95,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="Email (string)" MaxLength="(256)" Name="Email" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.EmailConfirmed" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="733.267997518851,16.2567470464467,138.64,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="EmailConfirmed (bool)" MaxLength="None" Name="EmailConfirmed" PropertyAccessMode="PropertyAccessMode.Default" Type="bool" ValueGenerated="None" />
    <Node Id="ApplicationUser.FirstName" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="565.431189657522,-39.7033359613658,114.42,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="FirstName (string)" MaxLength="None" Name="FirstName" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="565.431029338512,-151.623597344422,70.8833333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (string)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.LastName" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="981.081480591767,-95.6633884516002,113.236666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="LastName (string)" MaxLength="None" Name="LastName" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.LockoutEnabled" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="901.908012167288,16.2567470464467,138.89,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="LockoutEnabled (bool)" MaxLength="None" Name="LockoutEnabled" PropertyAccessMode="PropertyAccessMode.Default" Type="bool" ValueGenerated="None" />
    <Node Id="ApplicationUser.LockoutEnd" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="565.431213664684,184.137179175353,180.573333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="LockoutEnd (DateTimeOffset?)" MaxLength="None" Name="LockoutEnd" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTimeOffset?" ValueGenerated="None" />
    <Node Id="ApplicationUser.NormalizedEmail" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 256" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="737.831166871064,72.2168300542592,150.156666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="True" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="NormalizedEmail (string)" MaxLength="(256)" Name="NormalizedEmail" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.NormalizedUserName" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 256" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="946.651179484996,128.176974097228,177.313333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="True" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="NormalizedUserName (string)" MaxLength="(256)" Name="NormalizedUserName" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.OTPCode" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="700.147874227835,-95.6633884516002,109.94,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="OTPCode (string)" MaxLength="None" Name="OTPCode" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.OTPExpiration" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="755.951162802054,128.176974097228,160.7,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="OTPExpiration (DateTime?)" MaxLength="None" Name="OTPExpiration" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="ApplicationUser.PasswordHash" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1022.06806587823,-39.7033359613658,136.68,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="PasswordHash (string)" MaxLength="None" Name="PasswordHash" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.Phone" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="893.234533163382,-151.623566826844,94.23,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="Phone (string)" MaxLength="None" Name="Phone" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.PhoneNumber" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="565.431321900361,16.256808081603,137.836666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="PhoneNumber (string)" MaxLength="None" Name="PhoneNumber" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.PhoneNumberConfirmed" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="776.00462634372,184.13705710504,187.526666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="PhoneNumberConfirmed (bool)" MaxLength="None" Name="PhoneNumberConfirmed" PropertyAccessMode="PropertyAccessMode.Default" Type="bool" ValueGenerated="None" />
    <Node Id="ApplicationUser.Provider" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="565.431202271455,-95.6634494867565,104.716666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="Provider (string)" MaxLength="None" Name="Provider" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.RefreshTokens" Category="Navigation Collection" Bounds="840.087882365856,-95.6634189691783,110.993333333333,25.96" Dependent="RefreshToken" Field="" Inverse="User" Label="RefreshTokens (*)" Name="RefreshTokens" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="List&lt;RefreshToken&gt;" />
    <Node Id="ApplicationUser.SecurityStamp" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="855.957893759085,-39.7033359613658,136.11,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="SecurityStamp (string)" MaxLength="None" Name="SecurityStamp" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.TwoFactorEnabled" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="917.987745240205,72.2168910894154,150.516666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="TwoFactorEnabled (bool)" MaxLength="None" Name="TwoFactorEnabled" PropertyAccessMode="PropertyAccessMode.Default" Type="bool" ValueGenerated="None" />
    <Node Id="ApplicationUser.UserName" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 256" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="709.851253540986,-39.7033054437877,116.106666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="UserName (string)" MaxLength="(256)" Name="UserName" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ApplicationUser.Wishlist" Category="Navigation Property" Bounds="666.314331747366,-151.623536309266,77.97,25.96" Dependent="Wishlist" Field="" Inverse="User" Label="Wishlist (1)" Name="Wishlist" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="Wishlist" />
    <Node Id="Cart" Category="EntityType" Annotations="Relational:TableName: Carts RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="1064.28543285791,-491.423842975931,301.606807183158,197.880405895996" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Cart" Name="Cart" />
    <Node Id="Cart.CartItems" Category="Navigation Collection" Bounds="1259.61893339125,-451.423703393006,86.2733333333333,25.96" Dependent="CartItem" Field="" Inverse="Cart" Label="CartItems (*)" Name="CartItems" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="ICollection&lt;CartItem&gt;" />
    <Node Id="Cart.CreatedAt" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValueSql: GETUTCDATE()" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1208.95550240166,-395.463593682313,134.4,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CreatedAt (DateTime)" MaxLength="None" Name="CreatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Cart.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1084.28559273369,-451.423703393006,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Cart.UpdatedAt" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1084.28567655531,-339.503518303895,144.263333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="UpdatedAt (DateTime?)" MaxLength="None" Name="UpdatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="Cart.User" Category="Navigation Property" Bounds="1168.83561307875,-451.423703393006,60.7833333333333,25.96" Dependent="" Field="" Inverse="" Label="User (1)" Name="User" Principal="ApplicationUser" PropertyAccessMode="PropertyAccessMode.Default" Type="ApplicationUser" />
    <Node Id="Cart.UserId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1084.28546822198,-395.463593682313,94.6700000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="CartItem" Category="EntityType" Annotations="Relational:TableName: CartItems RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="1714.26352367822,-847.183541353413,289.650406087239,197.880419421387" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="CartItem" Name="CartItem" />
    <Node Id="CartItem.Cart" Category="Navigation Property" Bounds="1818.81400175062,-807.183438500428,58.7733333333333,25.96" Dependent="" Field="" Inverse="CartItems" Label="Cart (1)" Name="Cart" Principal="Cart" PropertyAccessMode="PropertyAccessMode.Default" Type="Cart" />
    <Node Id="CartItem.CartId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1907.58720080661,-807.183377465272,76.3266666666668,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CartId (int)" MaxLength="None" Name="CartId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="CartItem.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1734.26349800713,-807.183377465272,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="CartItem.Product" Category="Navigation Property" Bounds="1734.26423205661,-751.223263939881,78.3866666666668,25.96" Dependent="" Field="" Inverse="" Label="Product (1)" Name="Product" Principal="Product" PropertyAccessMode="PropertyAccessMode.Default" Type="Product" />
    <Node Id="CartItem.ProductId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1734.26387154229,-695.263211449647,95.9400000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ProductId (int)" MaxLength="None" Name="ProductId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="CartItem.Quantity" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValue: 1" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1842.65122749932,-751.22329445746,89.8700000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Quantity (int)" MaxLength="None" Name="Quantity" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Category" Category="EntityType" Annotations="Relational:TableName: Categories RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="2853.80862540348,-79.7029516887,206.563408203125,197.880444042969" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Category" Name="Category" />
    <Node Id="Category.Description" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 500" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2873.80866076755,72.2173793706654,120.876666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Description (string)" MaxLength="(500)" Name="Description" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Category.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2873.80866402276,-39.7028476801158,54.5500000000002,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Category.Name" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 100" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2873.80867378838,16.2572353276967,92.3200000000002,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Name (string)" MaxLength="(100)" Name="Name" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Category.Products" Category="Navigation Collection" Bounds="2958.35873563734,-39.7028476801158,82.0133333333333,25.96" Dependent="Product" Field="" Inverse="Category" Label="Products (*)" Name="Products" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="ICollection&lt;Product&gt;" />
    <Node Id="ChatHistory" Category="EntityType" Annotations="Relational:TableName: ChatHistories RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="1243.63241402225,290.090672254731,253.590173339844,197.88037167969" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="ChatHistory" Name="ChatHistory" UseManualLocation="True" />
    <Node Id="ChatHistory.BotResponse" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1348.18254055943,330.090767023246,129.04,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="BotResponse (string)" MaxLength="None" Name="BotResponse" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ChatHistory.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1263.63240506138,330.090767023246,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="ChatHistory.Timestamp" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1263.63227566685,442.010933038871,139.66,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Timestamp (DateTime)" MaxLength="None" Name="Timestamp" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="ChatHistory.UserMessage" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1263.63230740513,386.050850031059,131.1,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserMessage (string)" MaxLength="None" Name="UserMessage" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Comment" Category="EntityType" Annotations="Relational:TableName: Comments RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="146.254905514158,-903.144490892184,377.666747639975,309.800623236084" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Comment" Name="Comment" />
    <Node Id="Comment.Content" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="166.255020427379,-751.224057396913,103.073333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Content (string)" MaxLength="None" Name="Content" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Comment.CreatedAt" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValueSql: GETUTCDATE()" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="166.254895101858,-695.2639743891,134.4,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CreatedAt (DateTime)" MaxLength="None" Name="CreatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Comment.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="166.254953085257,-863.144238671327,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Comment.ParentComment" Category="Navigation Property" Bounds="299.328383057587,-751.224057396913,122.986666666667,25.96" Dependent="" Field="" Inverse="Replies" Label="ParentComment (1)" Name="ParentComment" Principal="Comment" PropertyAccessMode="PropertyAccessMode.Default" Type="Comment" />
    <Node Id="Comment.ParentCommentId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="166.255087972952,-639.30386086371,145.92,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="ParentCommentId (int?)" MaxLength="None" Name="ParentCommentId" PropertyAccessMode="PropertyAccessMode.Default" Type="int?" ValueGenerated="None" />
    <Node Id="Comment.Post" Category="Navigation Property" Bounds="250.80495450941,-863.144299706483,59.4633333333334,25.96" Dependent="" Field="" Inverse="Comments" Label="Post (1)" Name="Post" Principal="Post" PropertyAccessMode="PropertyAccessMode.Default" Type="Post" />
    <Node Id="Comment.PostId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="166.254957154267,-807.184170922303,77.0166666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="PostId (int)" MaxLength="None" Name="PostId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="Comment.Replies" Category="Navigation Collection" Bounds="431.051618124319,-863.144299706483,72.87,25.96" Dependent="Comment" Field="" Inverse="ParentComment" Label="Replies (*)" Name="Replies" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="ICollection&lt;Comment&gt;" />
    <Node Id="Comment.UpdatedAt" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="330.654985026988,-695.2639743891,144.263333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="UpdatedAt (DateTime?)" MaxLength="None" Name="UpdatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="Comment.User" Category="Navigation Property" Bounds="340.268276042613,-863.144299706483,60.7833333333333,25.96" Dependent="" Field="" Inverse="" Label="User (1)" Name="User" Principal="ApplicationUser" PropertyAccessMode="PropertyAccessMode.Default" Type="ApplicationUser" />
    <Node Id="Comment.UserId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="273.271628500296,-807.184170922303,94.67,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Comment.VoteCount" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValue: 0" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="397.941768270804,-807.18423195746,100.46,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="VoteCount (int)" MaxLength="None" Name="VoteCount" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Equipment" Category="EntityType" Annotations="Relational:TableName: Equipments RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="178.236004740397,433.652679614476,392.646767985548,253.840572827148" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Equipment" Name="Equipment" UseManualLocation="True" />
    <Node Id="Equipment.Battery" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="405.106042989618,473.652779614476,105.026666666667,25.96" Field="_battery" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Battery (double)" MaxLength="None" Name="Battery" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="Equipment.Connectivity" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="198.236220093296,641.533152441624,218.603333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Connectivity (EquipmentConnectivity)" MaxLength="None" Name="Connectivity" PropertyAccessMode="PropertyAccessMode.Default" Type="EquipmentConnectivity" ValueGenerated="None" />
    <Node Id="Equipment.EquipmentId" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="354.172746684279,529.612879614476,128.24,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="EquipmentId (string)" MaxLength="None" Name="EquipmentId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Equipment.HoursUsed" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="198.236004740397,529.612879614476,125.936666666667,25.96" Field="_hoursUsed" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="HoursUsed (double)" MaxLength="None" Name="HoursUsed" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="Equipment.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="198.236042989618,473.652779614476,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Equipment.LastMaintenance" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="380.339439392612,585.573008398655,170.543333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="LastMaintenance (DateTime)" MaxLength="None" Name="LastMaintenance" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="Equipment.Name" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="282.786042989618,473.652779614476,92.32,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Name (string)" MaxLength="None" Name="Name" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Equipment.Status" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="198.236052246616,585.573008398655,152.103333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Status (EquipmentStatus)" MaxLength="None" Name="Status" PropertyAccessMode="PropertyAccessMode.Default" Type="EquipmentStatus" ValueGenerated="None" />
    <Node Id="FeedbackEntry" Category="EntityType" Annotations="Relational:TableName: FeedbackEntries RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-969.14761038025,260.096674979596,277.530159505208,197.880505078125" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="FeedbackEntry" Name="FeedbackEntry" />
    <Node Id="FeedbackEntry.Disease" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-864.597377237009,300.096789160704,100.79,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Disease (string)" MaxLength="None" Name="Disease" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="FeedbackEntry.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-949.147421182321,300.096789160704,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="FeedbackEntry.ModelResult" Category="Navigation Property" Bounds="-949.147215290394,356.056872168517,103.013333333333,25.96" Dependent="" Field="" Inverse="FeedbackEntries" Label="ModelResult (1)" Name="ModelResult" Principal="ModelResult" PropertyAccessMode="PropertyAccessMode.Default" Type="ModelResult" />
    <Node Id="FeedbackEntry.ModelResultId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-949.147301146514,412.017077246642,120.566666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ModelResultId (int)" MaxLength="None" Name="ModelResultId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="FeedbackEntry.Solution" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-816.13392834378,356.056872168517,104.516666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Solution (string)" MaxLength="None" Name="Solution" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Field" Category="EntityType" Annotations="Relational:TableName: Fields RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-448.943380994996,-147.394379430934,365.456818393271,253.840605078125" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Field" Name="Field" UseManualLocation="True" />
    <Node Id="Field.Crop" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-344.393266248902,-107.394262438747,86.31,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Crop (string)" MaxLength="None" Name="Crop" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Field.HarvestDate" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-428.943229268392,4.52602564719091,146.043333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="HarvestDate (DateTime)" MaxLength="None" Name="HarvestDate" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="Field.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-428.943380994996,-107.394262438747,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Field.Irrigation" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-428.943144734701,60.4861256471909,152.116666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Irrigation (IrrigationType)" MaxLength="None" Name="Irrigation" PropertyAccessMode="PropertyAccessMode.Default" Type="IrrigationType" ValueGenerated="None" />
    <Node Id="Field.Name" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-428.943200581868,-51.4341624387466,92.32,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Name (string)" MaxLength="None" Name="Name" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Field.PlantingDate" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-252.899895935058,4.52602564719088,149.413333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="PlantingDate (DateTime)" MaxLength="None" Name="PlantingDate" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="Field.Size" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-228.083266248902,-107.394279430934,88.6333333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Size (double)" MaxLength="None" Name="Size" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="Field.Status" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-306.623200581869,-51.4341794309341,119.763333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Status (FieldStatus)" MaxLength="None" Name="Status" PropertyAccessMode="PropertyAccessMode.Default" Type="FieldStatus" ValueGenerated="None" />
    <Node Id="IModel" Category="Model" Annotations="BaseTypeDiscoveryConvention:DerivedTypes: System.Collections.Generic.Dictionary`2[System.Type,System.Collections.Generic.List`1[Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType]] Relational:MaxIdentifierLength: 128 RelationshipDiscoveryConvention:InverseNavigationCandidates: System.Collections.Generic.Dictionary`2[System.Type,System.Collections.Generic.SortedSet`1[System.Type]] SqlServer:ValueGenerationStrategy: IdentityColumn" Bounds="-989.1480074904,-943.14461261111,4363.70866729669,1664.96185330582" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" Label="AppDbContext" ProductVersion="8.0.10" PropertyAccessMode="PropertyAccessMode.Default" UseManualLocation="True" />
    <Node Id="IdentityRoleClaim&lt;string&gt;" Category="EntityType" Annotations="Relational:Schema: Relational:TableName: AspNetRoleClaims RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-969.1480074904,-491.423778849347,218.473393147787,197.880405895996" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="IdentityRoleClaim&lt;string&gt;" Name="IdentityRoleClaim&lt;string&gt;" />
    <Node Id="IdentityRoleClaim&lt;string&gt;.ClaimType" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-949.147887897816,-395.463563164735,115.073333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="ClaimType (string)" MaxLength="None" Name="ClaimType" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityRoleClaim&lt;string&gt;.ClaimValue" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-949.148033161488,-339.503487786317,118.963333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="ClaimValue (string)" MaxLength="None" Name="ClaimValue" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityRoleClaim&lt;string&gt;.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-949.148031533884,-451.423672875428,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="IdentityRoleClaim&lt;string&gt;.RoleId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-864.597973347035,-451.423672875428,93.9233333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="RoleId (string)" MaxLength="None" Name="RoleId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserClaim&lt;string&gt;" Category="EntityType" Annotations="Relational:Schema: Relational:TableName: AspNetUserClaims RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-516.521386252017,-506.276188665553,219.220000610352,197.880413525391" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="IdentityUserClaim&lt;string&gt;" Name="IdentityUserClaim&lt;string&gt;" />
    <Node Id="IdentityUserClaim&lt;string&gt;.ClaimType" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-496.521349427473,-410.315975140163,115.073333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="ClaimType (string)" MaxLength="None" Name="ClaimType" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserClaim&lt;string&gt;.ClaimValue" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-496.521372620832,-354.355875140163,118.963333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="ClaimValue (string)" MaxLength="None" Name="ClaimValue" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserClaim&lt;string&gt;.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-496.521386252017,-466.276088665553,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="IdentityUserClaim&lt;string&gt;.UserId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-411.971385641666,-466.276088665553,94.67,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserLogin&lt;string&gt;" Category="EntityType" Annotations="Relational:Schema: Relational:TableName: AspNetUserLogins RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="1395.95225658838,-491.423489626119,288.273553873698,197.880432446121" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="IdentityUserLogin&lt;string&gt;" Name="IdentityUserLogin&lt;string&gt;" />
    <Node Id="IdentityUserLogin&lt;string&gt;.LoginProvider" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1415.95233345635,-395.463288506532,134.16,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="LoginProvider (string)" MaxLength="None" Name="LoginProvider" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserLogin&lt;string&gt;.ProviderDisplayName" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1415.95229195244,-339.503182610536,174.7,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="ProviderDisplayName (string)" MaxLength="None" Name="ProviderDisplayName" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserLogin&lt;string&gt;.ProviderKey" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1540.62251249281,-451.423398217225,123.603333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ProviderKey (string)" MaxLength="None" Name="ProviderKey" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserLogin&lt;string&gt;.UserId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1415.95246040948,-451.423306664491,94.6700000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserRole&lt;string&gt;" Category="EntityType" Annotations="Relational:Schema: Relational:TableName: AspNetUserRoles RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-720.669608239098,-463.443722780415,173.516666666667,141.920302081299" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="IdentityUserRole&lt;string&gt;" Name="IdentityUserRole&lt;string&gt;" />
    <Node Id="IdentityUserRole&lt;string&gt;.RoleId" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-681.246227741566,-423.443627556825,93.9233333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="RoleId (string)" MaxLength="None" Name="RoleId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserRole&lt;string&gt;.UserId" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-681.246300576853,-367.483521660829,94.6700000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserToken&lt;string&gt;" Category="EntityType" Annotations="" BaseClass="" Bounds="2461.29244478011,-491.423490781717,251.597666015625,197.880409710693" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="IdentityUserToken&lt;string&gt;" Name="IdentityUserToken&lt;string&gt;" />
    <Node Id="IdentityUserToken&lt;string&gt;.LoginProvider" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2481.29248238213,-339.503182610536,134.16,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="LoginProvider (string)" MaxLength="None" Name="LoginProvider" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserToken&lt;string&gt;.Name" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2600.57014839776,-451.423398217225,92.3200000000002,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Name (string)" MaxLength="None" Name="Name" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserToken&lt;string&gt;.UserId" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2481.29248726494,-395.463288506532,94.6700000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="IdentityUserToken&lt;string&gt;.Value" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2481.2927427988,-451.423306664491,89.2766666666666,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="Value (string)" MaxLength="None" Name="Value" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Leasing" Category="EntityType" Annotations="Relational:TableName: Leasings RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="2792.65310294254,-875.16373544521,328.876723632813,253.840498614502" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Leasing" Name="Leasing" />
    <Node Id="Leasing.EndDate" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2812.65313830661,-779.203549584413,131.366666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="EndDate (DateTime?)" MaxLength="None" Name="EndDate" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="Leasing.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2812.65314644463,-835.163647851014,54.5500000000002,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Leasing.LeasingDetails" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2812.65342313734,-723.243436059022,136.283333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="LeasingDetails (string)" MaxLength="None" Name="LeasingDetails" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Leasing.Product" Category="Navigation Property" Bounds="2897.20319690036,-835.163647851014,78.3866666666668,25.96" Dependent="" Field="" Inverse="Leasings" Label="Product (1)" Name="Product" Principal="Product" PropertyAccessMode="PropertyAccessMode.Default" Type="Product" />
    <Node Id="Leasing.ProductId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="3005.58986031182,-835.163281640077,95.9400000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ProductId (int)" MaxLength="None" Name="ProductId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="Leasing.StartingDate" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2812.65314969984,-667.28335305121,147.223333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="StartingDate (DateTime)" MaxLength="None" Name="StartingDate" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="ModelResult" Category="EntityType" Annotations="Relational:TableName: AIModelResults RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-942.039060575562,503.936560575297,223.313399047852,197.880488085937" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="ModelResult" Name="ModelResult" />
    <Node Id="ModelResult.FeedbackEntries" Category="Navigation Collection" Bounds="-922.038827839222,655.856982031798,121.053333333333,25.96" Dependent="FeedbackEntry" Field="" Inverse="ModelResult" Label="FeedbackEntries (*)" Name="FeedbackEntries" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="List&lt;FeedbackEntry&gt;" />
    <Node Id="ModelResult.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-922.038961709665,543.************,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="ModelResult.ImageId" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-837.************,543.************,98.7633333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ImageId (Guid)" MaxLength="None" Name="ImageId" PropertyAccessMode="PropertyAccessMode.Default" Type="Guid" ValueGenerated="None" />
    <Node Id="ModelResult.ImageUrl" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-922.038940550811,599.************,108.************,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ImageUrl (string)" MaxLength="None" Name="ImageUrl" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Order" Category="EntityType" Annotations="Relational:Schema: Relational:TableName: Orders RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-267.079656515689,-534.************,364.************,253.************" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Order" Name="Order" />
    <Node Id="Order.CreatedAt" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValueSql: GETDATE()" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-247.079656515689,-382.335641985866,134.4,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CreatedAt (DateTime)" MaxLength="None" Name="CreatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Order.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-247.079621420474,-494.255869036647,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Order.OrderItems" Category="Navigation Collection" Bounds="-247.079605653059,-438.295755511256,95.2333333333333,25.96" Dependent="OrderItem" Field="" Inverse="Order" Label="OrderItems (*)" Name="OrderItems" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="ICollection&lt;OrderItem&gt;" />
    <Node Id="Order.ShippingAddress" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 200" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-72.8163170788398,-326.375589495631,150.073333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ShippingAddress (string)" MaxLength="(200)" Name="ShippingAddress" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Order.Status" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-121.846264436018,-438.295803021022,125.483333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Status (OrderStatus)" MaxLength="None" Name="Status" PropertyAccessMode="PropertyAccessMode.Default" Type="OrderStatus" ValueGenerated="None" />
    <Node Id="Order.TotalAmount" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:ColumnType: decimal(18,2)" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-82.6796473604154,-382.335703021022,139.65,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="TotalAmount (decimal)" MaxLength="None" Name="TotalAmount" PropertyAccessMode="PropertyAccessMode.Default" Type="decimal" ValueGenerated="None" />
    <Node Id="Order.UpdatedAt" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-247.079652039777,-326.375528460475,144.263333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="UpdatedAt (DateTime?)" MaxLength="None" Name="UpdatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="Order.User" Category="Navigation Property" Bounds="-162.529620148908,-494.255916546413,60.7833333333333,25.96" Dependent="" Field="" Inverse="" Label="User (1)" Name="User" Principal="ApplicationUser" PropertyAccessMode="PropertyAccessMode.Default" Type="ApplicationUser" />
    <Node Id="Order.UserId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-71.7462803560208,-494.255916546413,94.67,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="OrderItem" Category="EntityType" Annotations="Relational:Schema: Relational:TableName: OrderItems RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-227.530138524376,-862.036169646999,285.236705830892,197.8804" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="OrderItem" Name="OrderItem" />
    <Node Id="OrderItem.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-207.530138524376,-822.036069646999,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="OrderItem.Order" Category="Navigation Property" Bounds="-122.980138524376,-822.036069646999,67.7333333333333,25.96" Dependent="" Field="" Inverse="OrderItems" Label="Order (1)" Name="Order" Principal="Order" PropertyAccessMode="PropertyAccessMode.Default" Type="Order" />
    <Node Id="OrderItem.OrderId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-207.530090230309,-766.075969646999,85.2866666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="OrderId (int)" MaxLength="None" Name="OrderId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="OrderItem.ProductId" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-207.53009936015,-710.115869646999,95.94,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ProductId (int)" MaxLength="None" Name="ProductId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="OrderItem.Quantity" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-92.243399200442,-766.075969646999,89.87,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Quantity (int)" MaxLength="None" Name="Quantity" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="OrderItem.UnitPrice" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:ColumnType: decimal(18,2)" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-81.5900993601502,-710.115869646999,119.296666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UnitPrice (decimal)" MaxLength="None" Name="UnitPrice" PropertyAccessMode="PropertyAccessMode.Default" Type="decimal" ValueGenerated="None" />
    <Node Id="Post" Category="EntityType" Annotations="Relational:TableName: Posts RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="237.730369381346,-519.404131400783,422.716837565104,253.840673199632" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Post" Name="Post" />
    <Node Id="Post.Comments" Category="Navigation Collection" Bounds="257.730476156546,-423.443749627137,92.89,25.96" Dependent="Comment" Field="" Inverse="Post" Label="Comments (*)" Name="Comments" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="ICollection&lt;Comment&gt;" />
    <Node Id="Post.Content" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="388.190491008434,-367.483651360536,103.073333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Content (string)" MaxLength="None" Name="Content" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Post.CreatedAt" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValueSql: GETUTCDATE()" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="257.730358969046,-311.523560723329,134.4,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CreatedAt (DateTime)" MaxLength="None" Name="CreatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Post.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="257.7304779876,-479.403866967225,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Post.SharedPost" Category="Navigation Property" Bounds="505.290573202444,-423.443734368348,96.25,25.96" Dependent="" Field="" Inverse="Shares" Label="SharedPost (1)" Name="SharedPost" Principal="Post" PropertyAccessMode="PropertyAccessMode.Default" Type="Post" />
    <Node Id="Post.SharedPostId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="521.263908977184,-367.483651360536,119.183333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="SharedPostId (int?)" MaxLength="None" Name="SharedPostId" PropertyAccessMode="PropertyAccessMode.Default" Type="int?" ValueGenerated="None" />
    <Node Id="Post.Shares" Category="Navigation Collection" Bounds="433.063822307262,-479.403935631776,70.3433333333334,25.96" Dependent="Post" Field="" Inverse="SharedPost" Label="Shares (*)" Name="Shares" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="ICollection&lt;Post&gt;" />
    <Node Id="Post.Title" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 255" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="533.407203248017,-479.403836449647,83.0666666666666,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Title (string)" MaxLength="(255)" Name="Title" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Post.UpdatedAt" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="422.130372600231,-311.523560723329,144.263333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="UpdatedAt (DateTime?)" MaxLength="None" Name="UpdatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="Post.User" Category="Navigation Property" Bounds="342.280483073863,-479.40395851996,60.7833333333333,25.96" Dependent="" Field="" Inverse="" Label="User (1)" Name="User" Principal="ApplicationUser" PropertyAccessMode="PropertyAccessMode.Default" Type="ApplicationUser" />
    <Node Id="Post.UserId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="380.620459677054,-423.443841179872,94.67,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Post.VoteCount" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValue: 0" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="257.730479818655,-367.483651360536,100.46,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="VoteCount (int)" MaxLength="None" Name="VoteCount" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Product" Category="EntityType" Annotations="Relational:TableName: Products RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="2743.41744213525,-547.383426658626,427.347687174481,309.800649938965" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Product" Name="Product" />
    <Node Id="Product.Availability" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2763.41844429619,-283.542916258836,144.38,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Availability (Availability)" MaxLength="None" Name="Availability" PropertyAccessMode="PropertyAccessMode.Default" Type="Availability" ValueGenerated="None" />
    <Node Id="Product.Category" Category="Navigation Property" Bounds="3065.81183134046,-507.383336266542,84.9533333333334,25.96" Dependent="" Field="" Inverse="Products" Label="Category (1)" Name="Category" Principal="Category" PropertyAccessMode="PropertyAccessMode.Default" Type="Category" />
    <Node Id="Product.CategoryId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="3013.0283287363,-451.423245629335,102.506666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CategoryId (int)" MaxLength="None" Name="CategoryId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="Product.CreatedAt" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2763.41747749932,-339.503030022645,134.4,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CreatedAt (DateTime)" MaxLength="None" Name="CreatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="Product.Description" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 500" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2908.4116881113,-395.463105401063,120.876666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Description (string)" MaxLength="(500)" Name="Description" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Product.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2763.41828316338,-507.383305748963,54.5500000000002,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Product.Leasings" Category="Navigation Collection" Bounds="2955.29843290297,-507.383305748963,80.5133333333333,25.96" Dependent="Leasing" Field="" Inverse="Product" Label="Leasings (*)" Name="Leasings" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="ICollection&lt;Leasing&gt;" />
    <Node Id="Product.Name" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 100" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2763.41829292901,-451.423215111756,92.3200000000002,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Name (string)" MaxLength="(100)" Name="Name" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Product.Price" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Precision: 18 Scale: 4" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2885.73834663994,-451.423215111756,97.29,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Price (decimal)" MaxLength="None" Name="Price" PropertyAccessMode="PropertyAccessMode.Default" Type="decimal" ValueGenerated="None" />
    <Node Id="Product.ProductImages" Category="Navigation Collection" Bounds="2763.41829618421,-395.463105401063,114.993333333333,25.96" Dependent="ProductImage" Field="" Inverse="Product" Label="ProductImages (*)" Name="ProductImages" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="List&lt;ProductImage&gt;" />
    <Node Id="Product.Reviews" Category="Navigation Collection" Bounds="2847.96834663994,-507.383305748963,77.3299999999999,25.96" Dependent="Review" Field="" Inverse="Product" Label="Reviews (*)" Name="Reviews" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="ICollection&lt;Review&gt;" />
    <Node Id="Product.UpdatedAt" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2927.8179006764,-339.503030022645,138.883333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UpdatedAt (DateTime)" MaxLength="None" Name="UpdatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="ProductImage" Category="EntityType" Annotations="Relational:TableName: ProductImages RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="3151.62327221338,-847.183632906148,202.937259114584,197.880419421387" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="ProductImage" Name="ProductImage" />
    <Node Id="ProductImage.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="3171.62384956963,-807.183560570741,54.5500000000002,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="ProductImage.ImageUrl" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="3171.62387072849,-695.263303002381,108.************,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ImageUrl (string)" MaxLength="None" Name="ImageUrl" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="ProductImage.Product" Category="Navigation Property" Bounds="3256.17390002536,-807.183560570741,78.3866666666668,25.96" Dependent="" Field="" Inverse="ProductImages" Label="Product (1)" Name="Product" Principal="Product" PropertyAccessMode="PropertyAccessMode.Default" Type="Product" />
    <Node Id="ProductImage.ProductId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="3171.62330757744,-751.223416527772,95.9400000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ProductId (int)" MaxLength="None" Name="ProductId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="RefreshToken" Category="EntityType" Annotations="Relational:TableName: RefreshTokens RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="690.817001054523,-519.403936643534,342.543395182292,253.8405003479" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="RefreshToken" Name="RefreshToken" />
    <Node Id="RefreshToken.CreatedOn" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="874.930401083304,-367.483651360536,138.43,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CreatedOn (DateTime)" MaxLength="None" Name="CreatedOn" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="RefreshToken.ExpiresOn" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="710.817005901012,-367.483651360536,134.113333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ExpiresOn (DateTime)" MaxLength="None" Name="ExpiresOn" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="RefreshToken.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="710.817178427054,-479.403836449647,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="RefreshToken.JwtTokenId" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="835.487248820934,-423.443734368348,119.083333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="JwtTokenId (string)" MaxLength="None" Name="JwtTokenId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="RefreshToken.RevokedOn" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="710.817019735647,-311.523537835145,147.6,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="RevokedOn (DateTime?)" MaxLength="None" Name="RevokedOn" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="RefreshToken.Token" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="886.150582968069,-479.403836449647,91.8,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Token (string)" MaxLength="None" Name="Token" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="RefreshToken.User" Category="Navigation Property" Bounds="795.367168254528,-479.403836449647,60.7833333333333,25.96" Dependent="" Field="" Inverse="RefreshTokens" Label="User (1)" Name="User" Principal="ApplicationUser" PropertyAccessMode="PropertyAccessMode.Default" Type="ApplicationUser" />
    <Node Id="RefreshToken.UserId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="710.817175985647,-423.443734368348,94.67,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Review" Category="EntityType" Annotations="Relational:CheckConstraints: System.Collections.Generic.SortedDictionary`2[System.String,Microsoft.EntityFrameworkCore.Metadata.ICheckConstraint] Relational:TableName: Reviews RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="2376.13669669254,-875.163748189351,385.907037760417,253.840538506128" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Review" Name="Review" />
    <Node Id="Review.Headline" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 255" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2396.13728055922,-723.24331398871,107.473333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Headline (string)" MaxLength="(255)" Name="Headline" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Review.Product" Category="Navigation Property" Bounds="2486.92097033786,-835.163647851014,78.3866666666668,25.96" Dependent="" Field="" Inverse="Reviews" Label="Product (1)" Name="Product" Principal="Product" PropertyAccessMode="PropertyAccessMode.Default" Type="Product" />
    <Node Id="Review.ProductID" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2644.75710315036,-779.203549584413,97.2866666666669,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ProductID (int)" MaxLength="None" Name="ProductID" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="Review.Rating" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2595.30765653578,-835.163647851014,78.4033333333332,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Rating (int)" MaxLength="None" Name="Rating" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="Review.ReviewDate" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValueSql: GETUTCDATE()" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2396.13673205661,-667.283322533631,142.766666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ReviewDate (DateTime)" MaxLength="None" Name="ReviewDate" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Review.ReviewID" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2396.13673368421,-779.2034275141,92.6033333333335,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ReviewID (int)" MaxLength="None" Name="ReviewID" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Review.ReviewText" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2533.61062365817,-723.243436059022,118.296666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="ReviewText (string)" MaxLength="None" Name="ReviewText" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Review.UpdatedAt" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2568.90390165297,-667.28335305121,144.263333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="UpdatedAt (DateTime?)" MaxLength="None" Name="UpdatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="Review.User" Category="Navigation Property" Bounds="2396.13755399671,-835.163525780702,60.7833333333333,25.96" Dependent="" Field="" Inverse="" Label="User (1)" Name="User" Principal="ApplicationUser" PropertyAccessMode="PropertyAccessMode.Default" Type="ApplicationUser" />
    <Node Id="Review.UserID" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2518.74000354099,-779.203549584413,96.0166666666669,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserID (string)" MaxLength="None" Name="UserID" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Vote" Category="EntityType" Annotations="Relational:CheckConstraints: System.Collections.Generic.SortedDictionary`2[System.String,Microsoft.EntityFrameworkCore.Metadata.ICheckConstraint] Relational:TableName: Votes RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="1714.88083731754,-491.423436220357,322.416585286459,197.880545306397" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Vote" Name="Vote" />
    <Node Id="Vote.CreatedAt" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValueSql: GETUTCDATE()" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1882.89739693291,-339.503152092958,134.4,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CreatedAt (DateTime)" MaxLength="None" Name="CreatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Vote.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1734.88092964776,-451.423245629335,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Vote.TargetId" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1910.21461128838,-451.423306664491,87.1799999999998,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="TargetId (int)" MaxLength="None" Name="TargetId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="Vote.TargetType" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="MaxLength: 50" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1734.88081164645,-339.502991875672,118.016666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="TargetType (string)" MaxLength="(50)" Name="TargetType" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Vote.User" Category="Navigation Property" Bounds="1819.43119413343,-451.423337182069,60.7833333333333,25.96" Dependent="" Field="" Inverse="" Label="User (1)" Name="User" Principal="ApplicationUser" PropertyAccessMode="PropertyAccessMode.Default" Type="ApplicationUser" />
    <Node Id="Vote.UserId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1857.99750435479,-395.463227471376,94.6700000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Vote.VoteType" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="1734.88092150974,-395.463135918641,93.1166666666668,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="VoteType (int)" MaxLength="None" Name="VoteType" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="WeatherData" Category="EntityType" Annotations="Relational:TableName: WeatherDatas RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="-97.1079147111143,136.446325647191,370.770079526902,253.84061270752" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="WeatherData" Name="WeatherData" UseManualLocation="True" />
    <Node Id="WeatherData.Condition" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="65.1388931528726,232.406525647191,112.64,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Condition (string)" MaxLength="None" Name="Condition" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="WeatherData.Humidity" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-77.107763671875,288.36673835471,116.05,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Humidity (double)" MaxLength="None" Name="Humidity" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="WeatherData.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-77.1079147111142,176.446425647191,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="WeatherData.LastUpdated" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="87.1888725789388,344.32683835471,147.993333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="LastUpdated (DateTime)" MaxLength="None" Name="LastUpdated" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="WeatherData.Location" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="7.44208528888601,176.446425647191,105.746666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Location (string)" MaxLength="None" Name="Location" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="WeatherData.Pressure" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-77.1077735137966,232.406525647191,112.246666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Pressure (double)" MaxLength="None" Name="Pressure" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="WeatherData.Temperature" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="-77.1078212483724,344.32683835471,134.296666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Temperature (double)" MaxLength="None" Name="Temperature" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="WeatherData.UvIndex" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="143.188831482454,176.446425647191,110.473333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UvIndex (double)" MaxLength="None" Name="UvIndex" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="WeatherData.WindSpeed" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="68.942214050293,288.36673835471,128.67,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="WindSpeed (double)" MaxLength="None" Name="WindSpeed" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="WeatherForecast" Category="EntityType" Annotations="Relational:TableName: WeatherForecasts RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="671.776050104486,276.457325529492,394.616759440104,253.840666113281" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="WeatherForecast" Name="WeatherForecast" UseManualLocation="True" />
    <Node Id="WeatherForecast.Condition" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="691.776101987546,484.337857658398,112.64,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Condition (string)" MaxLength="None" Name="Condition" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="WeatherForecast.Date" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="940.332734067624,428.377591545117,106.06,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Date (DateTime)" MaxLength="None" Name="Date" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="None" />
    <Node Id="WeatherForecast.Day" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="776.326027524656,316.457425529492,80.95,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Day (string)" MaxLength="None" Name="Day" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="WeatherForecast.High" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="691.776102801348,428.37771361543,92.9333333333334,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="High (double)" MaxLength="None" Name="High" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="WeatherForecast.Humidity" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="929.666110532468,372.417508537305,92.6900000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Humidity (int)" MaxLength="None" Name="Humidity" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="WeatherForecast.Icon" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="887.276274106687,316.457425529492,83.1799999999999,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Icon (string)" MaxLength="None" Name="Icon" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="WeatherForecast.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="691.775966489499,316.457547599805,54.55,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="WeatherForecast.Low" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="810.662703143145,372.417508537305,89.0033333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Low (double)" MaxLength="None" Name="Low" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="WeatherForecast.Precipitation" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="834.415981544838,484.337735588086,134.196666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Precipitation (double)" MaxLength="None" Name="Precipitation" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="WeatherForecast.Pressure" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="691.775968117103,372.417630607617,88.8866666666667,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Pressure (int)" MaxLength="None" Name="Pressure" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="WeatherForecast.Wind" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="814.709453631426,428.377591545117,95.6233333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Wind (double)" MaxLength="None" Name="Wind" PropertyAccessMode="PropertyAccessMode.Default" Type="double" ValueGenerated="None" />
    <Node Id="Wishlist" Category="EntityType" Annotations="Relational:TableName: Wishlists RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="2068.08837475244,-491.423466737935,310.004204101562,197.88056819458" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="Wishlist" Name="Wishlist" />
    <Node Id="Wishlist.CreatedAt" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValueSql: GETUTCDATE()" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2223.55834663994,-395.463257988954,134.4,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CreatedAt (DateTime)" MaxLength="None" Name="CreatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Wishlist.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2088.08893746026,-451.423222741151,54.5500000000002,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="Wishlist.UpdatedAt" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2088.08920438734,-339.502999505067,144.263333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="UpdatedAt (DateTime?)" MaxLength="None" Name="UpdatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="Wishlist.User" Category="Navigation Property" Bounds="2172.63926298109,-451.423367699647,60.7833333333333,25.96" Dependent="" Field="" Inverse="Wishlist" Label="User (1)" Name="User" Principal="ApplicationUser" PropertyAccessMode="PropertyAccessMode.Default" Type="ApplicationUser" />
    <Node Id="Wishlist.UserId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2263.42261421807,-451.423306664491,94.6700000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="UserId (string)" MaxLength="None" Name="UserId" PropertyAccessMode="PropertyAccessMode.Default" Type="string" ValueGenerated="None" />
    <Node Id="Wishlist.WishlistItems" Category="Navigation Collection" Bounds="2088.08841011651,-395.463135918641,105.47,25.96" Dependent="WishlistItem" Field="" Inverse="Wishlist" Label="WishlistItems (*)" Name="WishlistItems" Principal="" PropertyAccessMode="PropertyAccessMode.Default" Type="ICollection&lt;WishlistItem&gt;" />
    <Node Id="WishlistItem" Category="EntityType" Annotations="Relational:TableName: WishlistItems RelationshipDiscoveryConvention:NavigationCandidates: System.Collections.Immutable.ImmutableSortedDictionary`2[System.Reflection.PropertyInfo,System.ValueTuple`2[System.Type,System.Nullable`1[System.Boolean]]]" BaseClass="" Bounds="2034.635703854,-875.163643892476,310.907737630208,253.840498614502" ChangeTrackingStrategy="ChangeTrackingStrategy.Snapshot" Group="Expanded" IsAbstract="False" Label="WishlistItem" Name="WishlistItem" />
    <Node Id="WishlistItem.CreatedAt" Category="Property Required" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="Relational:DefaultValueSql: GETUTCDATE()" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2054.63573921807,-723.243344506288,134.4,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="CreatedAt (DateTime)" MaxLength="None" Name="CreatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="WishlistItem.Id" Category="Property Primary" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="SqlServer:ValueGenerationStrategy: IdentityColumn" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2054.63630074151,-835.163525780702,54.5500000000002,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="True" IsRequired="True" IsShadow="False" IsUnicode="True" Label="Id (int)" MaxLength="None" Name="Id" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="ValueGenerated.OnAdd" />
    <Node Id="WishlistItem.Product" Category="Navigation Property" Bounds="2247.15681018161,-835.163525780702,78.3866666666668,25.96" Dependent="" Field="" Inverse="" Label="Product (1)" Name="Product" Principal="Product" PropertyAccessMode="PropertyAccessMode.Default" Type="Product" />
    <Node Id="WishlistItem.ProductId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2180.15992867119,-779.2034275141,95.9400000000001,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="ProductId (int)" MaxLength="None" Name="ProductId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
    <Node Id="WishlistItem.UpdatedAt" Category="Property Optional" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2054.63681180922,-667.283261498475,144.263333333333,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="False" IsIndexed="False" IsPrimaryKey="False" IsRequired="False" IsShadow="False" IsUnicode="True" Label="UpdatedAt (DateTime?)" MaxLength="None" Name="UpdatedAt" PropertyAccessMode="PropertyAccessMode.Default" Type="DateTime?" ValueGenerated="None" />
    <Node Id="WishlistItem.Wishlist" Category="Navigation Property" Bounds="2139.18631050713,-835.163525780702,77.9699999999998,25.96" Dependent="" Field="" Inverse="WishlistItems" Label="Wishlist (1)" Name="Wishlist" Principal="Wishlist" PropertyAccessMode="PropertyAccessMode.Default" Type="Wishlist" />
    <Node Id="WishlistItem.WishlistId" Category="Property Foreign" AfterSaveBehavior="PropertySaveBehavior.Save" Annotations="" BeforeSaveBehavior="PropertySaveBehavior.Save" Bounds="2054.6365725514,-779.2034275141,95.5233333333335,25.96" Field="" IsAlternateKey="False" IsConcurrencyToken="False" IsForeignKey="True" IsIndexed="True" IsPrimaryKey="False" IsRequired="True" IsShadow="False" IsUnicode="True" Label="WishlistId (int)" MaxLength="None" Name="WishlistId" PropertyAccessMode="PropertyAccessMode.Default" Type="int" ValueGenerated="None" />
  </Nodes>
  <Links>
    <Link Source="ApplicationRole" Target="ApplicationRole.ConcurrencyStamp" Category="Contains" />
    <Link Source="ApplicationRole" Target="ApplicationRole.Id" Category="Contains" />
    <Link Source="ApplicationRole" Target="ApplicationRole.Name" Category="Contains" />
    <Link Source="ApplicationRole" Target="ApplicationRole.NormalizedName" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.AccessFailedCount" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.Address" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.ConcurrencyStamp" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.Email" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.EmailConfirmed" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.FirstName" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.Id" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.LastName" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.LockoutEnabled" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.LockoutEnd" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.NormalizedEmail" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.NormalizedUserName" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.OTPCode" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.OTPExpiration" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.PasswordHash" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.Phone" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.PhoneNumber" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.PhoneNumberConfirmed" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.Provider" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.RefreshTokens" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.SecurityStamp" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.TwoFactorEnabled" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.UserName" Category="Contains" />
    <Link Source="ApplicationUser" Target="ApplicationUser.Wishlist" Category="Contains" />
    <Link Source="Cart" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="1048.73364257813,-293.544677734375,81.5260009765625,95.0872955322266" From="Cart.UserId" IsUnique="False" Label="1:*" LabelBounds="59.3567654814741,-1165.72797444419,14.0733333333337,15.96" Name="Cart -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="Cart" Target="Cart.CartItems" Category="Contains" />
    <Link Source="Cart" Target="Cart.CreatedAt" Category="Contains" />
    <Link Source="Cart" Target="Cart.Id" Category="Contains" />
    <Link Source="Cart" Target="Cart.UpdatedAt" Category="Contains" />
    <Link Source="Cart" Target="Cart.User" Category="Contains" />
    <Link Source="Cart" Target="Cart.UserId" Category="Contains" />
    <Link Source="CartItem" Target="Cart" Category="Foreign Key" Annotations="" Bounds="1373.76733398438,-668.239379882813,340.496337890625,187.719116210938" From="CartItem.CartId" IsUnique="False" Label="1:*" LabelBounds="533.867300886704,-1521.48727304358,14.0733333333333,15.96" Name="CartItem -&gt; Cart" To="Cart.Id" />
    <Link Source="CartItem" Target="CartItem.Cart" Category="Contains" />
    <Link Source="CartItem" Target="CartItem.CartId" Category="Contains" />
    <Link Source="CartItem" Target="CartItem.Id" Category="Contains" />
    <Link Source="CartItem" Target="CartItem.Product" Category="Contains" />
    <Link Source="CartItem" Target="CartItem.ProductId" Category="Contains" />
    <Link Source="CartItem" Target="CartItem.Quantity" Category="Contains" />
    <Link Source="CartItem" Target="Product" Category="Foreign Key" Annotations="" Bounds="1985.18664550781,-649.304016113281,749.706909179688,163.862152099609" From="CartItem.ProductId" IsUnique="False" Label="1:*" LabelBounds="1380.86729064218,-1521.48727304358,14.0733333333337,15.96" Name="CartItem -&gt; Product" To="Product.Id" />
    <Link Source="Category" Target="Category.Description" Category="Contains" />
    <Link Source="Category" Target="Category.Id" Category="Contains" />
    <Link Source="Category" Target="Category.Name" Category="Contains" />
    <Link Source="Category" Target="Category.Products" Category="Contains" />
    <Link Source="ChatHistory" Target="ChatHistory.BotResponse" Category="Contains" />
    <Link Source="ChatHistory" Target="ChatHistory.Id" Category="Contains" />
    <Link Source="ChatHistory" Target="ChatHistory.Timestamp" Category="Contains" />
    <Link Source="ChatHistory" Target="ChatHistory.UserMessage" Category="Contains" />
    <Link Source="Comment" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="523.921630859375,-625.676208496094,261.924743652344,425.551300048828" From="Comment.UserId" IsUnique="False" Label="1:*" LabelBounds="629.366968651839,-571.851507396815,14.0733333333337,15.96" Name="Comment -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="Comment" Target="Comment" Category="Foreign Key" Annotations="" Bounds="258.157806396484,-933.144470214844,147.894805908203,30" From="Comment.ParentCommentId" IsUnique="False" Label="1:*" LabelBounds="324.684525947246,-953.075194066764,14.0733333333337,15.96" Name="Comment -&gt; Comment" To="Comment.Id" />
    <Link Source="Comment" Target="Comment.Content" Category="Contains" />
    <Link Source="Comment" Target="Comment.CreatedAt" Category="Contains" />
    <Link Source="Comment" Target="Comment.Id" Category="Contains" />
    <Link Source="Comment" Target="Comment.ParentComment" Category="Contains" />
    <Link Source="Comment" Target="Comment.ParentCommentId" Category="Contains" />
    <Link Source="Comment" Target="Comment.Post" Category="Contains" />
    <Link Source="Comment" Target="Comment.PostId" Category="Contains" />
    <Link Source="Comment" Target="Comment.Replies" Category="Contains" />
    <Link Source="Comment" Target="Comment.UpdatedAt" Category="Contains" />
    <Link Source="Comment" Target="Comment.User" Category="Contains" />
    <Link Source="Comment" Target="Comment.UserId" Category="Contains" />
    <Link Source="Comment" Target="Comment.VoteCount" Category="Contains" />
    <Link Source="Comment" Target="Post" Category="Foreign Key" Annotations="" Bounds="384.724818738312,-593.343867656101,20.9469680557203,65.3690188363925" From="Comment.PostId" IsUnique="False" Label="1:*" LabelBounds="399.203080883864,-570.675677702386,14.0733333333337,15.96" Name="Comment -&gt; Post" To="Post.Id" />
    <Link Source="Equipment" Target="Equipment.Battery" Category="Contains" />
    <Link Source="Equipment" Target="Equipment.Connectivity" Category="Contains" />
    <Link Source="Equipment" Target="Equipment.EquipmentId" Category="Contains" />
    <Link Source="Equipment" Target="Equipment.HoursUsed" Category="Contains" />
    <Link Source="Equipment" Target="Equipment.Id" Category="Contains" />
    <Link Source="Equipment" Target="Equipment.LastMaintenance" Category="Contains" />
    <Link Source="Equipment" Target="Equipment.Name" Category="Contains" />
    <Link Source="Equipment" Target="Equipment.Status" Category="Contains" />
    <Link Source="FeedbackEntry" Target="FeedbackEntry.Disease" Category="Contains" />
    <Link Source="FeedbackEntry" Target="FeedbackEntry.Id" Category="Contains" />
    <Link Source="FeedbackEntry" Target="FeedbackEntry.ModelResult" Category="Contains" />
    <Link Source="FeedbackEntry" Target="FeedbackEntry.ModelResultId" Category="Contains" />
    <Link Source="FeedbackEntry" Target="FeedbackEntry.Solution" Category="Contains" />
    <Link Source="FeedbackEntry" Target="ModelResult" Category="Foreign Key" Annotations="" Bounds="-830.382934570313,457.973815917969,0,36.9599304199219" From="FeedbackEntry.ModelResultId" IsUnique="False" Label="1:*" LabelBounds="-1833.60433714523,-470.169345779406,14.0733333333333,15.9600000000002" Name="FeedbackEntry -&gt; ModelResult" To="ModelResult.Id" />
    <Link Source="Field" Target="Field.Crop" Category="Contains" />
    <Link Source="Field" Target="Field.HarvestDate" Category="Contains" />
    <Link Source="Field" Target="Field.Id" Category="Contains" />
    <Link Source="Field" Target="Field.Irrigation" Category="Contains" />
    <Link Source="Field" Target="Field.Name" Category="Contains" />
    <Link Source="Field" Target="Field.PlantingDate" Category="Contains" />
    <Link Source="Field" Target="Field.Size" Category="Contains" />
    <Link Source="Field" Target="Field.Status" Category="Contains" />
    <Link Source="IModel" Target="ApplicationRole" Category="Contains" />
    <Link Source="IModel" Target="ApplicationUser" Category="Contains" />
    <Link Source="IModel" Target="Cart" Category="Contains" />
    <Link Source="IModel" Target="CartItem" Category="Contains" />
    <Link Source="IModel" Target="Category" Category="Contains" />
    <Link Source="IModel" Target="ChatHistory" Category="Contains" />
    <Link Source="IModel" Target="Comment" Category="Contains" />
    <Link Source="IModel" Target="Equipment" Category="Contains" />
    <Link Source="IModel" Target="FeedbackEntry" Category="Contains" />
    <Link Source="IModel" Target="Field" Category="Contains" />
    <Link Source="IModel" Target="IdentityRoleClaim&lt;string&gt;" Category="Contains" />
    <Link Source="IModel" Target="IdentityUserClaim&lt;string&gt;" Category="Contains" />
    <Link Source="IModel" Target="IdentityUserLogin&lt;string&gt;" Category="Contains" />
    <Link Source="IModel" Target="IdentityUserRole&lt;string&gt;" Category="Contains" />
    <Link Source="IModel" Target="IdentityUserToken&lt;string&gt;" Category="Contains" />
    <Link Source="IModel" Target="Leasing" Category="Contains" />
    <Link Source="IModel" Target="ModelResult" Category="Contains" />
    <Link Source="IModel" Target="Order" Category="Contains" />
    <Link Source="IModel" Target="OrderItem" Category="Contains" />
    <Link Source="IModel" Target="Post" Category="Contains" />
    <Link Source="IModel" Target="Product" Category="Contains" />
    <Link Source="IModel" Target="ProductImage" Category="Contains" />
    <Link Source="IModel" Target="RefreshToken" Category="Contains" />
    <Link Source="IModel" Target="Review" Category="Contains" />
    <Link Source="IModel" Target="Vote" Category="Contains" />
    <Link Source="IModel" Target="WeatherData" Category="Contains" />
    <Link Source="IModel" Target="WeatherForecast" Category="Contains" />
    <Link Source="IModel" Target="Wishlist" Category="Contains" />
    <Link Source="IModel" Target="WishlistItem" Category="Contains" />
    <Link Source="IdentityRoleClaim&lt;string&gt;" Target="ApplicationRole" Category="Foreign Key" Annotations="" Bounds="-805.601440429688,-293.544677734375,113.049682617188,205.950035095215" From="IdentityRoleClaim&lt;string&gt;.RoleId" IsUnique="False" Label="1:*" LabelBounds="-1751.41802851138,-1165.72797444419,14.0733333333337,15.96" Name="IdentityRoleClaim&lt;string&gt; -&gt; ApplicationRole" To="ApplicationRole.Id" />
    <Link Source="IdentityRoleClaim&lt;string&gt;" Target="IdentityRoleClaim&lt;string&gt;.ClaimType" Category="Contains" />
    <Link Source="IdentityRoleClaim&lt;string&gt;" Target="IdentityRoleClaim&lt;string&gt;.ClaimValue" Category="Contains" />
    <Link Source="IdentityRoleClaim&lt;string&gt;" Target="IdentityRoleClaim&lt;string&gt;.Id" Category="Contains" />
    <Link Source="IdentityRoleClaim&lt;string&gt;" Target="IdentityRoleClaim&lt;string&gt;.RoleId" Category="Contains" />
    <Link Source="IdentityUserClaim&lt;string&gt;" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="-297.301385641666,-370.490744354554,834.20151663098,280.415591258536" From="IdentityUserClaim&lt;string&gt;.UserId" IsUnique="False" Label="1:*" LabelBounds="-195.407245032886,-354.309153832306,14.0733333333337,15.96" Name="IdentityUserClaim&lt;string&gt; -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="IdentityUserClaim&lt;string&gt;" Target="IdentityUserClaim&lt;string&gt;.ClaimType" Category="Contains" />
    <Link Source="IdentityUserClaim&lt;string&gt;" Target="IdentityUserClaim&lt;string&gt;.ClaimValue" Category="Contains" />
    <Link Source="IdentityUserClaim&lt;string&gt;" Target="IdentityUserClaim&lt;string&gt;.Id" Category="Contains" />
    <Link Source="IdentityUserClaim&lt;string&gt;" Target="IdentityUserClaim&lt;string&gt;.UserId" Category="Contains" />
    <Link Source="IdentityUserLogin&lt;string&gt;" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="1186.43981933594,-304.956726074219,209.51220703125,127.227493286133" From="IdentityUserLogin&lt;string&gt;.UserId" IsUnique="False" Label="1:*" LabelBounds="243.943375509646,-1165.72797444419,14.0733333333333,15.96" Name="IdentityUserLogin&lt;string&gt; -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="IdentityUserLogin&lt;string&gt;" Target="IdentityUserLogin&lt;string&gt;.LoginProvider" Category="Contains" />
    <Link Source="IdentityUserLogin&lt;string&gt;" Target="IdentityUserLogin&lt;string&gt;.ProviderDisplayName" Category="Contains" />
    <Link Source="IdentityUserLogin&lt;string&gt;" Target="IdentityUserLogin&lt;string&gt;.ProviderKey" Category="Contains" />
    <Link Source="IdentityUserLogin&lt;string&gt;" Target="IdentityUserLogin&lt;string&gt;.UserId" Category="Contains" />
    <Link Source="IdentityUserRole&lt;string&gt;" Target="ApplicationRole" Category="Foreign Key" Annotations="" Bounds="-633.911254882813,-321.524627685547,0,232.819534301758" From="IdentityUserRole&lt;string&gt;.RoleId" IsUnique="False" Label="1:*" LabelBounds="-1637.**********,-1165.72797444419,14.0733333333337,15.96" Name="IdentityUserRole&lt;string&gt; -&gt; ApplicationRole" To="ApplicationRole.Id" />
    <Link Source="IdentityUserRole&lt;string&gt;" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="-554.051452636719,-321.523406982422,1090.70788574219,266.523601531982" From="IdentityUserRole&lt;string&gt;.UserId" IsUnique="False" Label="1:*" LabelBounds="-463.921559214676,-303.830470595532,14.0733333333337,15.96" Name="IdentityUserRole&lt;string&gt; -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="IdentityUserRole&lt;string&gt;" Target="IdentityUserRole&lt;string&gt;.RoleId" Category="Contains" />
    <Link Source="IdentityUserRole&lt;string&gt;" Target="IdentityUserRole&lt;string&gt;.UserId" Category="Contains" />
    <Link Source="IdentityUserToken&lt;string&gt;" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="1187.65246582031,-293.544677734375,1329.86828613281,265.196147918701" From="IdentityUserToken&lt;string&gt;.UserId" IsUnique="False" Label="1:*" LabelBounds="1472.64821857908,-1166.25099662212,14.0733333333337,15.96" Name="IdentityUserToken&lt;string&gt; -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="IdentityUserToken&lt;string&gt;" Target="IdentityUserToken&lt;string&gt;.LoginProvider" Category="Contains" />
    <Link Source="IdentityUserToken&lt;string&gt;" Target="IdentityUserToken&lt;string&gt;.Name" Category="Contains" />
    <Link Source="IdentityUserToken&lt;string&gt;" Target="IdentityUserToken&lt;string&gt;.UserId" Category="Contains" />
    <Link Source="IdentityUserToken&lt;string&gt;" Target="IdentityUserToken&lt;string&gt;.Value" Category="Contains" />
    <Link Source="Leasing" Target="Leasing.EndDate" Category="Contains" />
    <Link Source="Leasing" Target="Leasing.Id" Category="Contains" />
    <Link Source="Leasing" Target="Leasing.LeasingDetails" Category="Contains" />
    <Link Source="Leasing" Target="Leasing.Product" Category="Contains" />
    <Link Source="Leasing" Target="Leasing.ProductId" Category="Contains" />
    <Link Source="Leasing" Target="Leasing.StartingDate" Category="Contains" />
    <Link Source="Leasing" Target="Product" Category="Foreign Key" Annotations="" Bounds="2957.08862304688,-621.324035644531,0,64.9398803710938" From="Leasing.ProductId" IsUnique="False" Label="1:*" LabelBounds="1953.8672837117,-1521.48727304358,14.0733333333337,15.96" Name="Leasing -&gt; Product" To="Product.Id" />
    <Link Source="ModelResult" Target="ModelResult.FeedbackEntries" Category="Contains" />
    <Link Source="ModelResult" Target="ModelResult.Id" Category="Contains" />
    <Link Source="ModelResult" Target="ModelResult.ImageId" Category="Contains" />
    <Link Source="ModelResult" Target="ModelResult.ImageUrl" Category="Contains" />
    <Link Source="Order" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="97.2570162544937,-325.278771537936,439.968108068055,198.181759720358" From="Order.UserId" IsUnique="False" Label="1:*" LabelBounds="211.5076993583,-291.968575320225,14.0733333333337,15.96" Name="Order -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="Order" Target="Order.CreatedAt" Category="Contains" />
    <Link Source="Order" Target="Order.Id" Category="Contains" />
    <Link Source="Order" Target="Order.OrderItems" Category="Contains" />
    <Link Source="Order" Target="Order.ShippingAddress" Category="Contains" />
    <Link Source="Order" Target="Order.Status" Category="Contains" />
    <Link Source="Order" Target="Order.TotalAmount" Category="Contains" />
    <Link Source="Order" Target="Order.UpdatedAt" Category="Contains" />
    <Link Source="Order" Target="Order.User" Category="Contains" />
    <Link Source="Order" Target="Order.UserId" Category="Contains" />
    <Link Source="OrderItem" Target="Order" Category="Foreign Key" Annotations="" Bounds="-84.9116561551202,-664.155769646999,0.000158185789061349,120.899753100594" From="OrderItem.OrderId" IsUnique="False" Label="1:*" LabelBounds="-80.9115770622256,-611.685895713511,14.0733333333337,15.96" Name="OrderItem -&gt; Order" To="Order.Id" />
    <Link Source="OrderItem" Target="OrderItem.Id" Category="Contains" />
    <Link Source="OrderItem" Target="OrderItem.Order" Category="Contains" />
    <Link Source="OrderItem" Target="OrderItem.OrderId" Category="Contains" />
    <Link Source="OrderItem" Target="OrderItem.ProductId" Category="Contains" />
    <Link Source="OrderItem" Target="OrderItem.Quantity" Category="Contains" />
    <Link Source="OrderItem" Target="OrderItem.UnitPrice" Category="Contains" />
    <Link Source="Post" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="576.403762451915,-265.563458201152,67.7957682098842,67.5856219534378" From="Post.UserId" IsUnique="False" Label="1:*" LabelBounds="611.713663199952,-249.147054298555,14.0733333333337,15.96" Name="Post -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="Post" Target="Post" Category="Foreign Key" Annotations="" Bounds="358.802276611328,-549.404113769531,174.327850341797,30" From="Post.SharedPostId" IsUnique="False" Label="1:*" LabelBounds="437.978140021194,-569.334834548679,14.0733333333337,15.96" Name="Post -&gt; Post" To="Post.Id" />
    <Link Source="Post" Target="Post.Comments" Category="Contains" />
    <Link Source="Post" Target="Post.Content" Category="Contains" />
    <Link Source="Post" Target="Post.CreatedAt" Category="Contains" />
    <Link Source="Post" Target="Post.Id" Category="Contains" />
    <Link Source="Post" Target="Post.SharedPost" Category="Contains" />
    <Link Source="Post" Target="Post.SharedPostId" Category="Contains" />
    <Link Source="Post" Target="Post.Shares" Category="Contains" />
    <Link Source="Post" Target="Post.Title" Category="Contains" />
    <Link Source="Post" Target="Post.UpdatedAt" Category="Contains" />
    <Link Source="Post" Target="Post.User" Category="Contains" />
    <Link Source="Post" Target="Post.UserId" Category="Contains" />
    <Link Source="Post" Target="Post.VoteCount" Category="Contains" />
    <Link Source="Product" Target="Category" Category="Foreign Key" Annotations="" Bounds="2957.08862304688,-237.58479309082,0,148.879699707031" From="Product.CategoryId" IsUnique="False" Label="1:*" LabelBounds="1953.8672837117,-1165.72797444419,14.0733333333337,15.96" Name="Product -&gt; Category" To="Category.Id" />
    <Link Source="Product" Target="Product.Availability" Category="Contains" />
    <Link Source="Product" Target="Product.Category" Category="Contains" />
    <Link Source="Product" Target="Product.CategoryId" Category="Contains" />
    <Link Source="Product" Target="Product.CreatedAt" Category="Contains" />
    <Link Source="Product" Target="Product.Description" Category="Contains" />
    <Link Source="Product" Target="Product.Id" Category="Contains" />
    <Link Source="Product" Target="Product.Leasings" Category="Contains" />
    <Link Source="Product" Target="Product.Name" Category="Contains" />
    <Link Source="Product" Target="Product.Price" Category="Contains" />
    <Link Source="Product" Target="Product.ProductImages" Category="Contains" />
    <Link Source="Product" Target="Product.Reviews" Category="Contains" />
    <Link Source="Product" Target="Product.UpdatedAt" Category="Contains" />
    <Link Source="ProductImage" Target="Product" Category="Foreign Key" Annotations="" Bounds="3091.72509765625,-649.304016113281,79.04345703125,95.0014038085938" From="ProductImage.ProductId" IsUnique="False" Label="1:*" LabelBounds="2101.86728192163,-1521.48727304358,14.0733333333337,15.96" Name="ProductImage -&gt; Product" To="Product.Id" />
    <Link Source="ProductImage" Target="ProductImage.Id" Category="Contains" />
    <Link Source="ProductImage" Target="ProductImage.ImageUrl" Category="Contains" />
    <Link Source="ProductImage" Target="ProductImage.Product" Category="Contains" />
    <Link Source="ProductImage" Target="ProductImage.ProductId" Category="Contains" />
    <Link Source="RefreshToken" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="862.088745117188,-265.564758300781,0,64.9398803710938" From="RefreshToken.UserId" IsUnique="False" Label="1:*" LabelBounds="-141.132690949125,-1165.72797444419,14.0733333333337,15.96" Name="RefreshToken -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="RefreshToken" Target="RefreshToken.CreatedOn" Category="Contains" />
    <Link Source="RefreshToken" Target="RefreshToken.ExpiresOn" Category="Contains" />
    <Link Source="RefreshToken" Target="RefreshToken.Id" Category="Contains" />
    <Link Source="RefreshToken" Target="RefreshToken.JwtTokenId" Category="Contains" />
    <Link Source="RefreshToken" Target="RefreshToken.RevokedOn" Category="Contains" />
    <Link Source="RefreshToken" Target="RefreshToken.Token" Category="Contains" />
    <Link Source="RefreshToken" Target="RefreshToken.User" Category="Contains" />
    <Link Source="RefreshToken" Target="RefreshToken.UserId" Category="Contains" />
    <Link Source="Review" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="1187.64770507813,-621.324035644531,1282.7958984375,591.789627075195" From="Review.UserID" IsUnique="False" Label="1:*" LabelBounds="1419.86729017047,-1343.60762374389,14.0733333333337,15.96" Name="Review -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="Review" Target="Product" Category="Foreign Key" Annotations="" Bounds="2707.51049804688,-621.324035644531,74.007080078125,67.8574829101563" From="Review.ProductID" IsUnique="False" Label="1:*" LabelBounds="1773.94061922126,-1521.48727304358,14.0733333333333,15.96" Name="Review -&gt; Product" To="Product.Id" />
    <Link Source="Review" Target="Review.Headline" Category="Contains" />
    <Link Source="Review" Target="Review.Product" Category="Contains" />
    <Link Source="Review" Target="Review.ProductID" Category="Contains" />
    <Link Source="Review" Target="Review.Rating" Category="Contains" />
    <Link Source="Review" Target="Review.ReviewDate" Category="Contains" />
    <Link Source="Review" Target="Review.ReviewID" Category="Contains" />
    <Link Source="Review" Target="Review.ReviewText" Category="Contains" />
    <Link Source="Review" Target="Review.UpdatedAt" Category="Contains" />
    <Link Source="Review" Target="Review.User" Category="Contains" />
    <Link Source="Review" Target="Review.UserID" Category="Contains" />
    <Link Source="Vote" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="1187.19177246094,-305.756469726563,527.688598632813,205.172096252441" From="Vote.UserId" IsUnique="False" Label="1:*" LabelBounds="438.83121574721,-1155.08467083549,14.0733333333338,15.96" Name="Vote -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="Vote" Target="Vote.CreatedAt" Category="Contains" />
    <Link Source="Vote" Target="Vote.Id" Category="Contains" />
    <Link Source="Vote" Target="Vote.TargetId" Category="Contains" />
    <Link Source="Vote" Target="Vote.TargetType" Category="Contains" />
    <Link Source="Vote" Target="Vote.User" Category="Contains" />
    <Link Source="Vote" Target="Vote.UserId" Category="Contains" />
    <Link Source="Vote" Target="Vote.VoteType" Category="Contains" />
    <Link Source="WeatherData" Target="WeatherData.Condition" Category="Contains" />
    <Link Source="WeatherData" Target="WeatherData.Humidity" Category="Contains" />
    <Link Source="WeatherData" Target="WeatherData.Id" Category="Contains" />
    <Link Source="WeatherData" Target="WeatherData.LastUpdated" Category="Contains" />
    <Link Source="WeatherData" Target="WeatherData.Location" Category="Contains" />
    <Link Source="WeatherData" Target="WeatherData.Pressure" Category="Contains" />
    <Link Source="WeatherData" Target="WeatherData.Temperature" Category="Contains" />
    <Link Source="WeatherData" Target="WeatherData.UvIndex" Category="Contains" />
    <Link Source="WeatherData" Target="WeatherData.WindSpeed" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Condition" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Date" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Day" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.High" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Humidity" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Icon" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Id" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Low" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Precipitation" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Pressure" Category="Contains" />
    <Link Source="WeatherForecast" Target="WeatherForecast.Wind" Category="Contains" />
    <Link Source="Wishlist" Target="ApplicationUser" Category="Foreign Key" Annotations="" Bounds="1187.46105957031,-303.428497314453,880.626098632813,238.611228942871" From="Wishlist.UserId" IsUnique="True" Label="1:1" LabelBounds="740.319289896119,-1168.06733168932,15.54,15.96" Name="Wishlist -&gt; ApplicationUser" To="ApplicationUser.Id" />
    <Link Source="Wishlist" Target="Wishlist.CreatedAt" Category="Contains" />
    <Link Source="Wishlist" Target="Wishlist.Id" Category="Contains" />
    <Link Source="Wishlist" Target="Wishlist.UpdatedAt" Category="Contains" />
    <Link Source="Wishlist" Target="Wishlist.User" Category="Contains" />
    <Link Source="Wishlist" Target="Wishlist.UserId" Category="Contains" />
    <Link Source="Wishlist" Target="Wishlist.WishlistItems" Category="Contains" />
    <Link Source="WishlistItem" Target="Product" Category="Foreign Key" Annotations="" Bounds="2345.5419921875,-633.796630859375,389.679443359375,138.566192626953" From="WishlistItem.ProductId" IsUnique="False" Label="1:*" LabelBounds="1584.42440131824,-1521.44188760513,14.0733333333337,15.96" Name="WishlistItem -&gt; Product" To="Product.Id" />
    <Link Source="WishlistItem" Target="Wishlist" Category="Foreign Key" Annotations="" Bounds="2201.86181640625,-621.324035644531,11.218017578125,120.938232421875" From="WishlistItem.WishlistId" IsUnique="False" Label="1:*" LabelBounds="1203.36729278905,-1521.48727304358,14.0733333333337,15.96" Name="WishlistItem -&gt; Wishlist" To="Wishlist.Id" />
    <Link Source="WishlistItem" Target="WishlistItem.CreatedAt" Category="Contains" />
    <Link Source="WishlistItem" Target="WishlistItem.Id" Category="Contains" />
    <Link Source="WishlistItem" Target="WishlistItem.Product" Category="Contains" />
    <Link Source="WishlistItem" Target="WishlistItem.ProductId" Category="Contains" />
    <Link Source="WishlistItem" Target="WishlistItem.UpdatedAt" Category="Contains" />
    <Link Source="WishlistItem" Target="WishlistItem.Wishlist" Category="Contains" />
    <Link Source="WishlistItem" Target="WishlistItem.WishlistId" Category="Contains" />
  </Links>
  <Categories>
    <Category Id="Contains" Label="Contains" Description="Whether the source of the link contains the target object" CanBeDataDriven="False" CanLinkedNodesBeDataDriven="True" IncomingActionLabel="Contained By" IsContainment="True" OutgoingActionLabel="Contains" />
    <Category Id="EntityType" />
    <Category Id="Foreign Key" />
    <Category Id="Model" />
    <Category Id="Navigation Collection" />
    <Category Id="Navigation Property" />
    <Category Id="Property Foreign" />
    <Category Id="Property Optional" />
    <Category Id="Property Primary" />
    <Category Id="Property Required" />
  </Categories>
  <Properties>
    <Property Id="AfterSaveBehavior" Group="Property Flags" DataType="System.String" />
    <Property Id="Annotations" Description="Annotations" Group="Model Properties" DataType="System.String" />
    <Property Id="BaseClass" Description="Base class" Group="Model Properties" DataType="System.String" />
    <Property Id="BeforeSaveBehavior" Group="Property Flags" DataType="System.String" />
    <Property Id="Bounds" DataType="System.Windows.Rect" />
    <Property Id="CanBeDataDriven" Label="CanBeDataDriven" Description="CanBeDataDriven" DataType="System.Boolean" />
    <Property Id="CanLinkedNodesBeDataDriven" Label="CanLinkedNodesBeDataDriven" Description="CanLinkedNodesBeDataDriven" DataType="System.Boolean" />
    <Property Id="ChangeTrackingStrategy" Description="Change tracking strategy" Group="Model Properties" DataType="System.String" />
    <Property Id="Dependent" Description="Dependent entity" Group="Model Properties" DataType="System.String" />
    <Property Id="Expression" DataType="System.String" />
    <Property Id="Field" Description="Backing field" Group="Model Properties" DataType="System.String" />
    <Property Id="From" Description="Target property" Group="Model Properties" DataType="System.String" />
    <Property Id="GraphDirection" DataType="Microsoft.VisualStudio.Diagrams.Layout.LayoutOrientation" />
    <Property Id="Group" Label="Group" Description="Display the node as a group" DataType="Microsoft.VisualStudio.GraphModel.GraphGroupStyle" />
    <Property Id="GroupLabel" DataType="System.String" />
    <Property Id="IncomingActionLabel" Label="IncomingActionLabel" Description="IncomingActionLabel" DataType="System.String" />
    <Property Id="Inverse" Description="Inverse entity" Group="Model Properties" DataType="System.String" />
    <Property Id="IsAbstract" Label="IsAbstract" Description="IsAbstract" Group="Model Properties" DataType="System.Boolean" />
    <Property Id="IsAlternateKey" Group="Property Flags" DataType="System.Boolean" />
    <Property Id="IsConcurrencyToken" Group="Property Flags" DataType="System.Boolean" />
    <Property Id="IsContainment" DataType="System.Boolean" />
    <Property Id="IsEnabled" DataType="System.Boolean" />
    <Property Id="IsForeignKey" Group="Property Flags" DataType="System.Boolean" />
    <Property Id="IsIndexed" Group="Property Flags" DataType="System.Boolean" />
    <Property Id="IsPrimaryKey" Group="Property Flags" DataType="System.Boolean" />
    <Property Id="IsRequired" Group="Property Flags" DataType="System.Boolean" />
    <Property Id="IsShadow" Group="Property Flags" DataType="System.Boolean" />
    <Property Id="IsUnicode" Group="Property Flags" DataType="System.Boolean" />
    <Property Id="IsUnique" Group="Model Properties" DataType="System.Boolean" />
    <Property Id="Label" Label="Label" Description="Displayable label of an Annotatable object" DataType="System.String" />
    <Property Id="LabelBounds" DataType="System.Windows.Rect" />
    <Property Id="MaxLength" DataType="System.String" />
    <Property Id="Name" Group="Model Properties" DataType="System.String" />
    <Property Id="OutgoingActionLabel" Label="OutgoingActionLabel" Description="OutgoingActionLabel" DataType="System.String" />
    <Property Id="Principal" Description="Principal entity" Group="Model Properties" DataType="System.String" />
    <Property Id="ProductVersion" Label="Product Version" Description="EF Core product version" Group="Model Properties" DataType="System.String" />
    <Property Id="PropertyAccessMode" Group="Property Flags" DataType="System.String" />
    <Property Id="TargetType" DataType="System.Type" />
    <Property Id="To" Description="Source property" Group="Model Properties" DataType="System.String" />
    <Property Id="Type" Description="CLR data type" Group="Model Properties" DataType="System.String" />
    <Property Id="UseManualLocation" DataType="System.Boolean" />
    <Property Id="Value" DataType="System.String" />
    <Property Id="ValueGenerated" Group="Property Flags" DataType="System.String" />
    <Property Id="ValueLabel" DataType="System.String" />
    <Property Id="ZoomLevel" DataType="System.String" />
  </Properties>
  <Styles>
    <Style TargetType="Node" GroupLabel="EntityType" ValueLabel="True">
      <Condition Expression="HasCategory('EntityType')" />
      <Setter Property="Background" Value="#FFC0C0C0" />
    </Style>
    <Style TargetType="Node" GroupLabel="Property Primary" ValueLabel="True">
      <Condition Expression="HasCategory('Property Primary')" />
      <Setter Property="Background" Value="#FF008000" />
    </Style>
    <Style TargetType="Node" GroupLabel="Property Optional" ValueLabel="True">
      <Condition Expression="HasCategory('Property Optional')" />
      <Setter Property="Background" Value="#FF808040" />
    </Style>
    <Style TargetType="Node" GroupLabel="Property Foreign" ValueLabel="True">
      <Condition Expression="HasCategory('Property Foreign')" />
      <Setter Property="Background" Value="#FF8080FF" />
    </Style>
    <Style TargetType="Node" GroupLabel="Property Required" ValueLabel="True">
      <Condition Expression="HasCategory('Property Required')" />
      <Setter Property="Background" Value="#FFC0A000" />
    </Style>
    <Style TargetType="Node" GroupLabel="Navigation Property" ValueLabel="True">
      <Condition Expression="HasCategory('Navigation Property')" />
      <Setter Property="Background" Value="#FF990000" />
    </Style>
    <Style TargetType="Node" GroupLabel="Navigation Collection" ValueLabel="True">
      <Condition Expression="HasCategory('Navigation Collection')" />
      <Setter Property="Background" Value="#FFFF3232" />
    </Style>
    <Style TargetType="Node" GroupLabel="Model" ValueLabel="True">
      <Condition Expression="HasCategory('Model')" />
      <Setter Property="Background" Value="#FFFFFFFF" />
    </Style>
  </Styles>
</DirectedGraph>
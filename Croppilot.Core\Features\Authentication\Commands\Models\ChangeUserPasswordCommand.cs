﻿namespace Croppilot.Core.Features.Authentication.Commands.Models
{
    public class ChangeUserPasswordCommand : IRequest<Response<string>>
    {
        public string Id { get; set; }
        public string CurrentPassword { get; set; }
        public string NewPassword { get; set; }
        //[Compare(nameof(NewPassword), ErrorMessage = "ConfirmPassword must match new password")]
        public string ConfirmPassword { get; set; }
        public ChangeUserPasswordCommand(string id, string currentPassword, string newPassword, string confirmPassword)
        {
            Id = id;
            CurrentPassword = currentPassword;
            NewPassword = newPassword;
            ConfirmPassword = confirmPassword;
        }
    }
}
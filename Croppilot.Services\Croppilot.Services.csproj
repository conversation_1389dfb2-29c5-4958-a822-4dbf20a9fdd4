﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
    <PackageReference Include="Google.Apis.Auth" Version="1.69.0" />
    <PackageReference Include="IVilson.AI.Yolov7net" Version="1.0.10" />
    <PackageReference Include="Mailjet.Api" Version="3.0.0" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.47.2" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.1" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.21.0" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="Stripe.net" Version="47.4.0" />
    <PackageReference Include="Twilio" Version="7.9.0" />
  </ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Croppilot.Infrastructure\Croppilot.Infrastructure.csproj" />
	</ItemGroup>
</Project>

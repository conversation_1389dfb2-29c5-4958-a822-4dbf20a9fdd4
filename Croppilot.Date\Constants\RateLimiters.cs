﻿namespace Croppilot.Date.Constants;

public class RateLimiters
{
    public const string ConcurrencyRateLimit = nameof(ConcurrencyRateLimit);
    public const string IpRateLimit = nameof(IpRateLimit);
    public const string UserIdRateLimit = nameof(UserIdRateLimit);
    public const string AIModelRateLimit = nameof(AIModelRateLimit);
    public const string ChatBotEndpointsLimit = nameof(ChatBotEndpointsLimit);
    public const string IoTEndpointsLimit = nameof(IoTEndpointsLimit);
    public const string AdminEndpointsLimit = nameof(AdminEndpointsLimit);
    public const string SocialEndpointsLimit = nameof(SocialEndpointsLimit);
    public const string PaymentEndpointsLimit = nameof(PaymentEndpointsLimit);
    public const string ReadOperationsLimit = nameof(ReadOperationsLimit);
    public const string WriteOperationsLimit = nameof(WriteOperationsLimit);
    public const string AuthenticationEndpointsLimit = nameof(AuthenticationEndpointsLimit);
}